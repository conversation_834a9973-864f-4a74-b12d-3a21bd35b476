'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var InteractionList;
var InteractionPage;
var InteractionInstance;
var InteractionContext;

/* jshint ignore:start */
/**
 * Initialize the InteractionList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList
 *
 * @param {Twilio.Proxy.V1} version - Version of the resource
 * @param {string} serviceSid - The SID of the resource's parent Service
 * @param {string} sessionSid - The SID of the resource's parent Session
 */
/* jshint ignore:end */
InteractionList = function InteractionList(version, serviceSid, sessionSid) {
  /* jshint ignore:start */
  /**
   * @function interactions
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext}
   */
  /* jshint ignore:end */
  function InteractionListInstance(sid) {
    return InteractionListInstance.get(sid);
  }

  InteractionListInstance._version = version;
  // Path Solution
  InteractionListInstance._solution = {serviceSid: serviceSid, sessionSid: sessionSid};
  InteractionListInstance._uri = `/Services/${serviceSid}/Sessions/${sessionSid}/Interactions`;
  /* jshint ignore:start */
  /**
   * Streams InteractionInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  InteractionListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists InteractionInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of InteractionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of InteractionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a interaction
   *
   * @function get
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext}
   */
  /* jshint ignore:end */
  InteractionListInstance.get = function get(sid) {
    return new InteractionContext(
      this._version,
      this._solution.serviceSid,
      this._solution.sessionSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  InteractionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  InteractionListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return InteractionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the InteractionPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {InteractionSolution} solution - Path solution
 *
 * @returns InteractionPage
 */
/* jshint ignore:end */
InteractionPage = function InteractionPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(InteractionPage.prototype, Page.prototype);
InteractionPage.prototype.constructor = InteractionPage;

/* jshint ignore:start */
/**
 * Build an instance of InteractionInstance
 *
 * @function getInstance
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionPage#
 *
 * @param {InteractionPayload} payload - Payload response from the API
 *
 * @returns InteractionInstance
 */
/* jshint ignore:end */
InteractionPage.prototype.getInstance = function getInstance(payload) {
  return new InteractionInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.sessionSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} sessionSid - The SID of the resource's parent Session
 * @property {string} serviceSid - The SID of the resource's parent Service
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} data -
 *          A JSON string that includes the message body of message interactions
 * @property {interaction.type} type - The Type of the Interaction
 * @property {string} inboundParticipantSid -
 *          The SID of the inbound Participant resource
 * @property {string} inboundResourceSid - The SID of the inbound resource
 * @property {interaction.resource_status} inboundResourceStatus -
 *          The inbound resource status of the Interaction
 * @property {string} inboundResourceType - The inbound resource type
 * @property {string} inboundResourceUrl - The URL of the Twilio inbound resource
 * @property {string} outboundParticipantSid - The SID of the outbound Participant
 * @property {string} outboundResourceSid - The SID of the outbound resource
 * @property {interaction.resource_status} outboundResourceStatus -
 *          The outbound resource status of the Interaction
 * @property {string} outboundResourceType - The outbound resource type
 * @property {string} outboundResourceUrl - The URL of the Twilio outbound resource
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the Interaction was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the Interaction resource
 *
 * @param {V1} version - Version of the resource
 * @param {InteractionPayload} payload - The instance payload
 * @param {sid} serviceSid - The SID of the resource's parent Service
 * @param {sid} sessionSid - The SID of the resource's parent Session
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
InteractionInstance = function InteractionInstance(version, payload, serviceSid,
                                                    sessionSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.sessionSid = payload.session_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.data = payload.data; // jshint ignore:line
  this.type = payload.type; // jshint ignore:line
  this.inboundParticipantSid = payload.inbound_participant_sid; // jshint ignore:line
  this.inboundResourceSid = payload.inbound_resource_sid; // jshint ignore:line
  this.inboundResourceStatus = payload.inbound_resource_status; // jshint ignore:line
  this.inboundResourceType = payload.inbound_resource_type; // jshint ignore:line
  this.inboundResourceUrl = payload.inbound_resource_url; // jshint ignore:line
  this.outboundParticipantSid = payload.outbound_participant_sid; // jshint ignore:line
  this.outboundResourceSid = payload.outbound_resource_sid; // jshint ignore:line
  this.outboundResourceStatus = payload.outbound_resource_status; // jshint ignore:line
  this.outboundResourceType = payload.outbound_resource_type; // jshint ignore:line
  this.outboundResourceUrl = payload.outbound_resource_url; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sessionSid: sessionSid, sid: sid || this.sid, };
};

Object.defineProperty(InteractionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new InteractionContext(
          this._version,
          this._solution.serviceSid,
          this._solution.sessionSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a InteractionInstance
 *
 * @function fetch
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a InteractionInstance
 *
 * @function remove
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} serviceSid - The SID of the parent Service of the resource to fetch
 * @param {sid} sessionSid - he SID of the parent Session of the resource to fetch
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
InteractionContext = function InteractionContext(version, serviceSid,
                                                  sessionSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sessionSid: sessionSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Sessions/${sessionSid}/Interactions/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a InteractionInstance
 *
 * @function fetch
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new InteractionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sessionSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a InteractionInstance
 *
 * @function remove
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Proxy.V1.ServiceContext.SessionContext.InteractionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

InteractionContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  InteractionList: InteractionList,
  InteractionPage: InteractionPage,
  InteractionInstance: InteractionInstance,
  InteractionContext: InteractionContext
};
