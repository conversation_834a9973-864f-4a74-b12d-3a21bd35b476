'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var FunctionVersionList = require(
    './function/functionVersion').FunctionVersionList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var FunctionList;
var FunctionPage;
var FunctionInstance;
var FunctionContext;

/* jshint ignore:start */
/**
 * Initialize the FunctionList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionList
 *
 * @param {Twilio.Serverless.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the Function resource is associated with
 */
/* jshint ignore:end */
FunctionList = function FunctionList(version, serviceSid) {
  /* jshint ignore:start */
  /**
   * @function functions
   * @memberof Twilio.Serverless.V1.ServiceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext}
   */
  /* jshint ignore:end */
  function FunctionListInstance(sid) {
    return FunctionListInstance.get(sid);
  }

  FunctionListInstance._version = version;
  // Path Solution
  FunctionListInstance._solution = {serviceSid: serviceSid};
  FunctionListInstance._uri = `/Services/${serviceSid}/Functions`;
  /* jshint ignore:start */
  /**
   * Streams FunctionInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  FunctionListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists FunctionInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of FunctionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FunctionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of FunctionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new FunctionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * create a FunctionInstance
   *
   * @function create
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.friendlyName - A string to describe the Function resource
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed FunctionInstance
   */
  /* jshint ignore:end */
  FunctionListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['friendlyName'])) {
      throw new Error('Required parameter "opts[\'friendlyName\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({'FriendlyName': _.get(opts, 'friendlyName')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FunctionInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a function
   *
   * @function get
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @param {string} sid - The SID of the Function resource to fetch
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext}
   */
  /* jshint ignore:end */
  FunctionListInstance.get = function get(sid) {
    return new FunctionContext(this._version, this._solution.serviceSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FunctionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FunctionListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return FunctionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FunctionPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FunctionSolution} solution - Path solution
 *
 * @returns FunctionPage
 */
/* jshint ignore:end */
FunctionPage = function FunctionPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FunctionPage.prototype, Page.prototype);
FunctionPage.prototype.constructor = FunctionPage;

/* jshint ignore:start */
/**
 * Build an instance of FunctionInstance
 *
 * @function getInstance
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionPage#
 *
 * @param {FunctionPayload} payload - Payload response from the API
 *
 * @returns FunctionInstance
 */
/* jshint ignore:end */
FunctionPage.prototype.getInstance = function getInstance(payload) {
  return new FunctionInstance(this._version, payload, this._solution.serviceSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FunctionPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FunctionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionInstance
 *
 * @property {string} sid - The unique string that identifies the Function resource
 * @property {string} accountSid -
 *          The SID of the Account that created the Function resource
 * @property {string} serviceSid -
 *          The SID of the Service that the Function resource is associated with
 * @property {string} friendlyName -
 *          The string that you assigned to describe the Function resource
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the Function resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the Function resource was last updated
 * @property {string} url - The absolute URL of the Function resource
 * @property {string} links - The URLs of nested resources of the Function resource
 *
 * @param {V1} version - Version of the resource
 * @param {FunctionPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the Function resource is associated with
 * @param {sid} sid - The SID of the Function resource to fetch
 */
/* jshint ignore:end */
FunctionInstance = function FunctionInstance(version, payload, serviceSid, sid)
                                              {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sid: sid || this.sid, };
};

Object.defineProperty(FunctionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new FunctionContext(this._version, this._solution.serviceSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a FunctionInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a FunctionInstance
 *
 * @function remove
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * update a FunctionInstance
 *
 * @function update
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionInstance#
 *
 * @param {object} opts - Options for request
 * @param {string} opts.friendlyName - A string to describe the Function resource
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Access the functionVersions
 *
 * @function functionVersions
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionInstance#
 *
 * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList}
 */
/* jshint ignore:end */
FunctionInstance.prototype.functionVersions = function functionVersions() {
  return this._proxy.functionVersions;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FunctionInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FunctionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionContext
 *
 * @property {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList} functionVersions -
 *          functionVersions resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Service to fetch the Function resource from
 * @param {sid} sid - The SID of the Function resource to fetch
 */
/* jshint ignore:end */
FunctionContext = function FunctionContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Functions/${sid}`;

  // Dependents
  this._functionVersions = undefined;
};

/* jshint ignore:start */
/**
 * fetch a FunctionInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new FunctionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a FunctionInstance
 *
 * @function remove
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a FunctionInstance
 *
 * @function update
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext#
 *
 * @param {object} opts - Options for request
 * @param {string} opts.friendlyName - A string to describe the Function resource
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionInstance
 */
/* jshint ignore:end */
FunctionContext.prototype.update = function update(opts, callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['friendlyName'])) {
    throw new Error('Required parameter "opts[\'friendlyName\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({'FriendlyName': _.get(opts, 'friendlyName')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new FunctionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(FunctionContext.prototype,
  'functionVersions', {
    get: function() {
      if (!this._functionVersions) {
        this._functionVersions = new FunctionVersionList(
          this._version,
          this._solution.serviceSid,
          this._solution.sid
        );
      }
      return this._functionVersions;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

FunctionContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FunctionList: FunctionList,
  FunctionPage: FunctionPage,
  FunctionInstance: FunctionInstance,
  FunctionContext: FunctionContext
};
