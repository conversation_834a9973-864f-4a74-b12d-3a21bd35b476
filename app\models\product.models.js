//Required external modules
var mongoose = require('mongoose');

// Setup schema
var userSchema = mongoose.Schema({
    title: {type:String},
    sku: {type:String},
    category: {type:String},
    brand: {type:String},
    description: {type:String},
    variant: {type:String},
    poster_image: {type:String},
    raw_poster_image:{type:String},
    multiple_image:[],
    available:{type:Number,default:0},
    topProducts:{type:Number,default:0},
    orderProducts:{type:Number,default:0},
    notes:{type:String},
    // items: [],
    status:{type:Boolean,default:true},
    variant_type:[],
        banners: [{ type: mongoose.Schema.Types.ObjectId }]

}, { timestamps: true });


// Export user model for FAQs 
userSchema.index({ request: "text" });

var Products = module.exports = mongoose.model('product', userSchema);
module.exports.get = function (callback, limit) {
    Products.find(callback).limit(limit);
}
