'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var BucketList;
var BucketPage;
var BucketInstance;
var BucketContext;

/* jshint ignore:start */
/**
 * Initialize the BucketList
 *
 * @constructor Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList
 *
 * @param {Twilio.Verify.V2} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {string} rateLimitSid - Rate Limit Sid.
 */
/* jshint ignore:end */
BucketList = function BucketList(version, serviceSid, rateLimitSid) {
  /* jshint ignore:start */
  /**
   * @function buckets
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext}
   */
  /* jshint ignore:end */
  function BucketListInstance(sid) {
    return BucketListInstance.get(sid);
  }

  BucketListInstance._version = version;
  // Path Solution
  BucketListInstance._solution = {serviceSid: serviceSid, rateLimitSid: rateLimitSid};
  BucketListInstance._uri = `/Services/${serviceSid}/RateLimits/${rateLimitSid}/Buckets`;
  /* jshint ignore:start */
  /**
   * create a BucketInstance
   *
   * @function create
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {object} opts - Options for request
   * @param {number} opts.max - Max number of requests.
   * @param {number} opts.interval -
   *          Number of seconds that the rate limit will be enforced over.
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed BucketInstance
   */
  /* jshint ignore:end */
  BucketListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['max'])) {
      throw new Error('Required parameter "opts[\'max\']" missing.');
    }
    if (_.isUndefined(opts['interval'])) {
      throw new Error('Required parameter "opts[\'interval\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({'Max': _.get(opts, 'max'), 'Interval': _.get(opts, 'interval')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BucketInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.rateLimitSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams BucketInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  BucketListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists BucketInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BucketListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of BucketInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BucketListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BucketPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of BucketInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BucketListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new BucketPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a bucket
   *
   * @function get
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @param {string} sid - A string that uniquely identifies this Bucket.
   *
   * @returns {Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext}
   */
  /* jshint ignore:end */
  BucketListInstance.get = function get(sid) {
    return new BucketContext(
      this._version,
      this._solution.serviceSid,
      this._solution.rateLimitSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BucketListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BucketListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return BucketListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BucketPage
 *
 * @constructor Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketPage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BucketSolution} solution - Path solution
 *
 * @returns BucketPage
 */
/* jshint ignore:end */
BucketPage = function BucketPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BucketPage.prototype, Page.prototype);
BucketPage.prototype.constructor = BucketPage;

/* jshint ignore:start */
/**
 * Build an instance of BucketInstance
 *
 * @function getInstance
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketPage#
 *
 * @param {BucketPayload} payload - Payload response from the API
 *
 * @returns BucketInstance
 */
/* jshint ignore:end */
BucketPage.prototype.getInstance = function getInstance(payload) {
  return new BucketInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.rateLimitSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BucketPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BucketPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BucketContext
 *
 * @constructor Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketInstance
 *
 * @property {string} sid - A string that uniquely identifies this Bucket.
 * @property {string} rateLimitSid - Rate Limit Sid.
 * @property {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {number} max - Max number of requests.
 * @property {number} interval -
 *          Number of seconds that the rate limit will be enforced over.
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} url - The URL of this resource.
 *
 * @param {V2} version - Version of the resource
 * @param {BucketPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {sid} rateLimitSid - Rate Limit Sid.
 * @param {sid} sid - A string that uniquely identifies this Bucket.
 */
/* jshint ignore:end */
BucketInstance = function BucketInstance(version, payload, serviceSid,
                                          rateLimitSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.rateLimitSid = payload.rate_limit_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.max = deserialize.integer(payload.max); // jshint ignore:line
  this.interval = deserialize.integer(payload.interval); // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, rateLimitSid: rateLimitSid, sid: sid || this.sid, };
};

Object.defineProperty(BucketInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BucketContext(
          this._version,
          this._solution.serviceSid,
          this._solution.rateLimitSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * update a BucketInstance
 *
 * @function update
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {number} [opts.max] - Max number of requests.
 * @param {number} [opts.interval] -
 *          Number of seconds that the rate limit will be enforced over.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * fetch a BucketInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a BucketInstance
 *
 * @function remove
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BucketInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BucketInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BucketContext
 *
 * @constructor Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext
 *
 * @param {V2} version - Version of the resource
 * @param {sid} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {sid} rateLimitSid - Rate Limit Sid.
 * @param {sid} sid - A string that uniquely identifies this Bucket.
 */
/* jshint ignore:end */
BucketContext = function BucketContext(version, serviceSid, rateLimitSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, rateLimitSid: rateLimitSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/RateLimits/${rateLimitSid}/Buckets/${sid}`;
};

/* jshint ignore:start */
/**
 * update a BucketInstance
 *
 * @function update
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext#
 *
 * @param {object} [opts] - Options for request
 * @param {number} [opts.max] - Max number of requests.
 * @param {number} [opts.interval] -
 *          Number of seconds that the rate limit will be enforced over.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({'Max': _.get(opts, 'max'), 'Interval': _.get(opts, 'interval')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new BucketInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.rateLimitSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * fetch a BucketInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BucketInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.rateLimitSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a BucketInstance
 *
 * @function remove
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BucketInstance
 */
/* jshint ignore:end */
BucketContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.RateLimitContext.BucketContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BucketContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BucketContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BucketList: BucketList,
  BucketPage: BucketPage,
  BucketInstance: BucketInstance,
  BucketContext: BucketContext
};
