'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var serialize = require(
    '../../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var InteractionChannelParticipantList;
var InteractionChannelParticipantPage;
var InteractionChannelParticipantInstance;
var InteractionChannelParticipantContext;

/* jshint ignore:start */
/**
 * Initialize the InteractionChannelParticipantList
 *
 * @constructor Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList
 *
 * @param {Twilio.FlexApi.V1} version - Version of the resource
 * @param {string} interactionSid - The Interaction Sid for this channel.
 * @param {string} channelSid - The Channel Sid for this Participant.
 */
/* jshint ignore:end */
InteractionChannelParticipantList = function
    InteractionChannelParticipantList(version, interactionSid, channelSid) {
  /* jshint ignore:start */
  /**
   * @function participants
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantContext}
   */
  /* jshint ignore:end */
  function InteractionChannelParticipantListInstance(sid) {
    return InteractionChannelParticipantListInstance.get(sid);
  }

  InteractionChannelParticipantListInstance._version = version;
  // Path Solution
  InteractionChannelParticipantListInstance._solution = {
    interactionSid: interactionSid,
    channelSid: channelSid
  };
  InteractionChannelParticipantListInstance._uri = `/Interactions/${interactionSid}/Channels/${channelSid}/Participants`;
  /* jshint ignore:start */
  /**
   * create a InteractionChannelParticipantInstance
   *
   * @function create
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {object} opts - Options for request
   * @param {interaction_channel_participant.type} opts.type - Participant type.
   * @param {object} opts.mediaProperties -
   *          JSON representing the Media Properties for the new Participant.
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed InteractionChannelParticipantInstance
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.create = function create(opts,
      callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['type'])) {
      throw new Error('Required parameter "opts[\'type\']" missing.');
    }
    if (_.isUndefined(opts['mediaProperties'])) {
      throw new Error('Required parameter "opts[\'mediaProperties\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'Type': _.get(opts, 'type'),
      'MediaProperties': serialize.object(_.get(opts, 'mediaProperties'))
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionChannelParticipantInstance(
        this._version,
        payload,
        this._solution.interactionSid,
        this._solution.channelSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams InteractionChannelParticipantInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists InteractionChannelParticipantInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of InteractionChannelParticipantInstance records from the
   * API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionChannelParticipantPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of InteractionChannelParticipantInstance records
   * from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.getPage = function getPage(targetUrl,
      callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionChannelParticipantPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a interaction_channel_participant
   *
   * @function get
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantContext}
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.get = function get(sid) {
    return new InteractionChannelParticipantContext(
      this._version,
      this._solution.interactionSid,
      this._solution.channelSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  InteractionChannelParticipantListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  InteractionChannelParticipantListInstance[util.inspect.custom] = function
      inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return InteractionChannelParticipantListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the InteractionChannelParticipantPage
 *
 * @constructor Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {InteractionChannelParticipantSolution} solution - Path solution
 *
 * @returns InteractionChannelParticipantPage
 */
/* jshint ignore:end */
InteractionChannelParticipantPage = function
    InteractionChannelParticipantPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(InteractionChannelParticipantPage.prototype, Page.prototype);
InteractionChannelParticipantPage.prototype.constructor = InteractionChannelParticipantPage;

/* jshint ignore:start */
/**
 * Build an instance of InteractionChannelParticipantInstance
 *
 * @function getInstance
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantPage#
 *
 * @param {InteractionChannelParticipantPayload} payload -
 *          Payload response from the API
 *
 * @returns InteractionChannelParticipantInstance
 */
/* jshint ignore:end */
InteractionChannelParticipantPage.prototype.getInstance = function
    getInstance(payload) {
  return new InteractionChannelParticipantInstance(
    this._version,
    payload,
    this._solution.interactionSid,
    this._solution.channelSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionChannelParticipantPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionChannelParticipantPage.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionChannelParticipantContext
 *
 * @constructor Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {interaction_channel_participant.type} type - Participant type.
 * @property {string} interactionSid - The Interaction Sid for this channel.
 * @property {string} channelSid - The Channel Sid for this Participant.
 * @property {string} url - The url
 *
 * @param {V1} version - Version of the resource
 * @param {InteractionChannelParticipantPayload} payload - The instance payload
 * @param {sid} interactionSid - The Interaction Sid for this channel.
 * @param {sid} channelSid - The Channel Sid for this Participant.
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
InteractionChannelParticipantInstance = function
    InteractionChannelParticipantInstance(version, payload, interactionSid,
    channelSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.type = payload.type; // jshint ignore:line
  this.interactionSid = payload.interaction_sid; // jshint ignore:line
  this.channelSid = payload.channel_sid; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {interactionSid: interactionSid, channelSid: channelSid, sid: sid || this.sid, };
};

Object.defineProperty(InteractionChannelParticipantInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new InteractionChannelParticipantContext(
          this._version,
          this._solution.interactionSid,
          this._solution.channelSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * update a InteractionChannelParticipantInstance
 *
 * @function update
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantInstance#
 *
 * @param {object} opts - Options for request
 * @param {interaction_channel_participant.status} opts.status -
 *          The Participant's status.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionChannelParticipantInstance
 */
/* jshint ignore:end */
InteractionChannelParticipantInstance.prototype.update = function update(opts,
    callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionChannelParticipantInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionChannelParticipantInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionChannelParticipantContext
 *
 * @constructor Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} interactionSid - The Interaction Sid for this channel.
 * @param {sid} channelSid - The Channel Sid for this Participant.
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
InteractionChannelParticipantContext = function
    InteractionChannelParticipantContext(version, interactionSid, channelSid,
    sid) {
  this._version = version;

  // Path Solution
  this._solution = {interactionSid: interactionSid, channelSid: channelSid, sid: sid, };
  this._uri = `/Interactions/${interactionSid}/Channels/${channelSid}/Participants/${sid}`;
};

/* jshint ignore:start */
/**
 * update a InteractionChannelParticipantInstance
 *
 * @function update
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantContext#
 *
 * @param {object} opts - Options for request
 * @param {interaction_channel_participant.status} opts.status -
 *          The Participant's status.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionChannelParticipantInstance
 */
/* jshint ignore:end */
InteractionChannelParticipantContext.prototype.update = function update(opts,
    callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['status'])) {
    throw new Error('Required parameter "opts[\'status\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({'Status': _.get(opts, 'status')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new InteractionChannelParticipantInstance(
      this._version,
      payload,
      this._solution.interactionSid,
      this._solution.channelSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionContext.InteractionChannelContext.InteractionChannelParticipantContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionChannelParticipantContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

InteractionChannelParticipantContext.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  InteractionChannelParticipantList: InteractionChannelParticipantList,
  InteractionChannelParticipantPage: InteractionChannelParticipantPage,
  InteractionChannelParticipantInstance: InteractionChannelParticipantInstance,
  InteractionChannelParticipantContext: InteractionChannelParticipantContext
};
