'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var DeviceList;
var DevicePage;
var DeviceInstance;
var DeviceContext;

/* jshint ignore:start */
/**
 * Initialize the DeviceList
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Microvisor.V1.DeviceList
 *
 * @param {Twilio.Microvisor.V1} version - Version of the resource
 */
/* jshint ignore:end */
DeviceList = function DeviceList(version) {
  /* jshint ignore:start */
  /**
   * @function devices
   * @memberof Twilio.Microvisor.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Microvisor.V1.DeviceContext}
   */
  /* jshint ignore:end */
  function DeviceListInstance(sid) {
    return DeviceListInstance.get(sid);
  }

  DeviceListInstance._version = version;
  // Path Solution
  DeviceListInstance._solution = {};
  DeviceListInstance._uri = `/Devices`;
  /* jshint ignore:start */
  /**
   * Streams DeviceInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  DeviceListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists DeviceInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeviceListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of DeviceInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeviceListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new DevicePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of DeviceInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeviceListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new DevicePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a device
   *
   * @function get
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @param {string} sid - A string that uniquely identifies this Device.
   *
   * @returns {Twilio.Microvisor.V1.DeviceContext}
   */
  /* jshint ignore:end */
  DeviceListInstance.get = function get(sid) {
    return new DeviceContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Microvisor.V1.DeviceList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DeviceListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DeviceListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return DeviceListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DevicePage
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Microvisor.V1.DevicePage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DeviceSolution} solution - Path solution
 *
 * @returns DevicePage
 */
/* jshint ignore:end */
DevicePage = function DevicePage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DevicePage.prototype, Page.prototype);
DevicePage.prototype.constructor = DevicePage;

/* jshint ignore:start */
/**
 * Build an instance of DeviceInstance
 *
 * @function getInstance
 * @memberof Twilio.Microvisor.V1.DevicePage#
 *
 * @param {DevicePayload} payload - Payload response from the API
 *
 * @returns DeviceInstance
 */
/* jshint ignore:end */
DevicePage.prototype.getInstance = function getInstance(payload) {
  return new DeviceInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Microvisor.V1.DevicePage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DevicePage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DevicePage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeviceContext
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Microvisor.V1.DeviceInstance
 *
 * @property {string} sid - A string that uniquely identifies this Device.
 * @property {string} uniqueName -
 *          A developer-defined string that uniquely identifies the Device.
 * @property {string} accountSid - Account SID.
 * @property {object} app -
 *          Information about the target App and the App reported by this Device.
 * @property {object} logging -
 *          Object specifying whether application logging is enabled for this Device.
 * @property {Date} dateCreated - The date that this Device was created.
 * @property {Date} dateUpdated - The date that this Device was last updated.
 * @property {string} url - The URL of this resource.
 * @property {string} links - The absolute URLs of related resources
 *
 * @param {V1} version - Version of the resource
 * @param {DevicePayload} payload - The instance payload
 * @param {sid_like} sid - A string that uniquely identifies this Device.
 */
/* jshint ignore:end */
DeviceInstance = function DeviceInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.uniqueName = payload.unique_name; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.app = payload.app; // jshint ignore:line
  this.logging = payload.logging; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(DeviceInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DeviceContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a DeviceInstance
 *
 * @function fetch
 * @memberof Twilio.Microvisor.V1.DeviceInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeviceInstance
 */
/* jshint ignore:end */
DeviceInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a DeviceInstance
 *
 * @function update
 * @memberof Twilio.Microvisor.V1.DeviceInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.uniqueName] -
 *          A unique, developer-assigned name for this Device.
 * @param {string} [opts.targetApp] - The target App SID or unique name.
 * @param {boolean} [opts.loggingEnabled] - Whether to enable logging.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeviceInstance
 */
/* jshint ignore:end */
DeviceInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Microvisor.V1.DeviceInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeviceInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DeviceInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeviceContext
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Microvisor.V1.DeviceContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} sid - A string that uniquely identifies this Device.
 */
/* jshint ignore:end */
DeviceContext = function DeviceContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/Devices/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a DeviceInstance
 *
 * @function fetch
 * @memberof Twilio.Microvisor.V1.DeviceContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeviceInstance
 */
/* jshint ignore:end */
DeviceContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new DeviceInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a DeviceInstance
 *
 * @function update
 * @memberof Twilio.Microvisor.V1.DeviceContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.uniqueName] -
 *          A unique, developer-assigned name for this Device.
 * @param {string} [opts.targetApp] - The target App SID or unique name.
 * @param {boolean} [opts.loggingEnabled] - Whether to enable logging.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeviceInstance
 */
/* jshint ignore:end */
DeviceContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'UniqueName': _.get(opts, 'uniqueName'),
    'TargetApp': _.get(opts, 'targetApp'),
    'LoggingEnabled': serialize.bool(_.get(opts, 'loggingEnabled'))
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new DeviceInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Microvisor.V1.DeviceContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeviceContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DeviceContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DeviceList: DeviceList,
  DevicePage: DevicePage,
  DeviceInstance: DeviceInstance,
  DeviceContext: DeviceContext
};
