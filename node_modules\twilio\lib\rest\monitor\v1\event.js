'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var EventList;
var EventPage;
var EventInstance;
var EventContext;

/* jshint ignore:start */
/**
 * Initialize the EventList
 *
 * @constructor Twilio.Monitor.V1.EventList
 *
 * @param {Twilio.Monitor.V1} version - Version of the resource
 */
/* jshint ignore:end */
EventList = function EventList(version) {
  /* jshint ignore:start */
  /**
   * @function events
   * @memberof Twilio.Monitor.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Monitor.V1.EventContext}
   */
  /* jshint ignore:end */
  function EventListInstance(sid) {
    return EventListInstance.get(sid);
  }

  EventListInstance._version = version;
  // Path Solution
  EventListInstance._solution = {};
  EventListInstance._uri = `/Events`;
  /* jshint ignore:start */
  /**
   * Streams EventInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.actorSid] - Only include events initiated by this Actor
   * @param {string} [opts.eventType] - Only include events of this Event Type
   * @param {string} [opts.resourceSid] -
   *          Only include events that refer to this resource
   * @param {string} [opts.sourceIpAddress] -
   *          Only include events that originated from this IP address
   * @param {Date} [opts.startDate] -
   *          Only include events that occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include events that occurred on or before this date
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  EventListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists EventInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.actorSid] - Only include events initiated by this Actor
   * @param {string} [opts.eventType] - Only include events of this Event Type
   * @param {string} [opts.resourceSid] -
   *          Only include events that refer to this resource
   * @param {string} [opts.sourceIpAddress] -
   *          Only include events that originated from this IP address
   * @param {Date} [opts.startDate] -
   *          Only include events that occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include events that occurred on or before this date
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EventListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of EventInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.actorSid] - Only include events initiated by this Actor
   * @param {string} [opts.eventType] - Only include events of this Event Type
   * @param {string} [opts.resourceSid] -
   *          Only include events that refer to this resource
   * @param {string} [opts.sourceIpAddress] -
   *          Only include events that originated from this IP address
   * @param {Date} [opts.startDate] -
   *          Only include events that occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include events that occurred on or before this date
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EventListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'ActorSid': _.get(opts, 'actorSid'),
      'EventType': _.get(opts, 'eventType'),
      'ResourceSid': _.get(opts, 'resourceSid'),
      'SourceIpAddress': _.get(opts, 'sourceIpAddress'),
      'StartDate': serialize.iso8601DateTime(_.get(opts, 'startDate')),
      'EndDate': serialize.iso8601DateTime(_.get(opts, 'endDate')),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EventPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of EventInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EventListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new EventPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a event
   *
   * @function get
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @param {string} sid - The SID that identifies the resource to fetch
   *
   * @returns {Twilio.Monitor.V1.EventContext}
   */
  /* jshint ignore:end */
  EventListInstance.get = function get(sid) {
    return new EventContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Monitor.V1.EventList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  EventListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  EventListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return EventListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the EventPage
 *
 * @constructor Twilio.Monitor.V1.EventPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {EventSolution} solution - Path solution
 *
 * @returns EventPage
 */
/* jshint ignore:end */
EventPage = function EventPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(EventPage.prototype, Page.prototype);
EventPage.prototype.constructor = EventPage;

/* jshint ignore:start */
/**
 * Build an instance of EventInstance
 *
 * @function getInstance
 * @memberof Twilio.Monitor.V1.EventPage#
 *
 * @param {EventPayload} payload - Payload response from the API
 *
 * @returns EventInstance
 */
/* jshint ignore:end */
EventPage.prototype.getInstance = function getInstance(payload) {
  return new EventInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Monitor.V1.EventPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
EventPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EventPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EventContext
 *
 * @constructor Twilio.Monitor.V1.EventInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} actorSid -
 *          The SID of the actor that caused the event, if available
 * @property {string} actorType - The type of actor that caused the event
 * @property {string} description - A description of the event
 * @property {object} eventData -
 *          A JSON string that represents an object with additional data about the event
 * @property {Date} eventDate -
 *          The ISO 8601 date and time in GMT when the event was recorded
 * @property {string} eventType - The event's type
 * @property {string} resourceSid - The SID of the resource that was affected
 * @property {string} resourceType - The type of resource that was affected
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} source -
 *          The originating system or interface that caused the event
 * @property {string} sourceIpAddress - The IP address of the source
 * @property {string} url - The absolute URL of the resource that was affected
 * @property {string} links - The absolute URLs of related resources
 *
 * @param {V1} version - Version of the resource
 * @param {EventPayload} payload - The instance payload
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
EventInstance = function EventInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.actorSid = payload.actor_sid; // jshint ignore:line
  this.actorType = payload.actor_type; // jshint ignore:line
  this.description = payload.description; // jshint ignore:line
  this.eventData = payload.event_data; // jshint ignore:line
  this.eventDate = deserialize.iso8601DateTime(payload.event_date); // jshint ignore:line
  this.eventType = payload.event_type; // jshint ignore:line
  this.resourceSid = payload.resource_sid; // jshint ignore:line
  this.resourceType = payload.resource_type; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.source = payload.source; // jshint ignore:line
  this.sourceIpAddress = payload.source_ip_address; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(EventInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new EventContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a EventInstance
 *
 * @function fetch
 * @memberof Twilio.Monitor.V1.EventInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EventInstance
 */
/* jshint ignore:end */
EventInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Monitor.V1.EventInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
EventInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EventInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EventContext
 *
 * @constructor Twilio.Monitor.V1.EventContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
EventContext = function EventContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/Events/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a EventInstance
 *
 * @function fetch
 * @memberof Twilio.Monitor.V1.EventContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EventInstance
 */
/* jshint ignore:end */
EventContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new EventInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Monitor.V1.EventContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
EventContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

EventContext.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  EventList: EventList,
  EventPage: EventPage,
  EventInstance: EventInstance,
  EventContext: EventContext
};
