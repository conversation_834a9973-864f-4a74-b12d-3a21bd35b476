'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var ChallengeList = require('./entity/challenge').ChallengeList;
var FactorList = require('./entity/factor').FactorList;
var NewFactorList = require('./entity/newFactor').NewFactorList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var EntityList;
var EntityPage;
var EntityInstance;
var EntityContext;

/* jshint ignore:start */
/**
 * Initialize the EntityList
 *
 * @constructor Twilio.Verify.V2.ServiceContext.EntityList
 *
 * @param {Twilio.Verify.V2} version - Version of the resource
 * @param {string} serviceSid - Service Sid.
 */
/* jshint ignore:end */
EntityList = function EntityList(version, serviceSid) {
  /* jshint ignore:start */
  /**
   * @function entities
   * @memberof Twilio.Verify.V2.ServiceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Verify.V2.ServiceContext.EntityContext}
   */
  /* jshint ignore:end */
  function EntityListInstance(sid) {
    return EntityListInstance.get(sid);
  }

  EntityListInstance._version = version;
  // Path Solution
  EntityListInstance._solution = {serviceSid: serviceSid};
  EntityListInstance._uri = `/Services/${serviceSid}/Entities`;
  /* jshint ignore:start */
  /**
   * create a EntityInstance
   *
   * @function create
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.identity - Unique external identifier of the Entity
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed EntityInstance
   */
  /* jshint ignore:end */
  EntityListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['identity'])) {
      throw new Error('Required parameter "opts[\'identity\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({'Identity': _.get(opts, 'identity')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EntityInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.identity
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams EntityInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  EntityListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists EntityInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EntityListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of EntityInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EntityListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EntityPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of EntityInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EntityListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new EntityPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a entity
   *
   * @function get
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @param {string} identity - Unique external identifier of the Entity
   *
   * @returns {Twilio.Verify.V2.ServiceContext.EntityContext}
   */
  /* jshint ignore:end */
  EntityListInstance.get = function get(identity) {
    return new EntityContext(this._version, this._solution.serviceSid, identity);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Verify.V2.ServiceContext.EntityList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  EntityListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  EntityListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return EntityListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the EntityPage
 *
 * @constructor Twilio.Verify.V2.ServiceContext.EntityPage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {EntitySolution} solution - Path solution
 *
 * @returns EntityPage
 */
/* jshint ignore:end */
EntityPage = function EntityPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(EntityPage.prototype, Page.prototype);
EntityPage.prototype.constructor = EntityPage;

/* jshint ignore:start */
/**
 * Build an instance of EntityInstance
 *
 * @function getInstance
 * @memberof Twilio.Verify.V2.ServiceContext.EntityPage#
 *
 * @param {EntityPayload} payload - Payload response from the API
 *
 * @returns EntityInstance
 */
/* jshint ignore:end */
EntityPage.prototype.getInstance = function getInstance(payload) {
  return new EntityInstance(this._version, payload, this._solution.serviceSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.EntityPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
EntityPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EntityPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EntityContext
 *
 * @constructor Twilio.Verify.V2.ServiceContext.EntityInstance
 *
 * @property {string} sid - A string that uniquely identifies this Entity.
 * @property {string} identity - Unique external identifier of the Entity
 * @property {string} accountSid - Account Sid.
 * @property {string} serviceSid - Service Sid.
 * @property {Date} dateCreated - The date this Entity was created
 * @property {Date} dateUpdated - The date this Entity was updated
 * @property {string} url - The URL of this resource.
 * @property {string} links - Nested resource URLs.
 *
 * @param {V2} version - Version of the resource
 * @param {EntityPayload} payload - The instance payload
 * @param {sid} serviceSid - Service Sid.
 * @param {string} identity - Unique external identifier of the Entity
 */
/* jshint ignore:end */
EntityInstance = function EntityInstance(version, payload, serviceSid, identity)
                                          {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.identity = payload.identity; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, identity: identity || this.identity, };
};

Object.defineProperty(EntityInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new EntityContext(
          this._version,
          this._solution.serviceSid,
          this._solution.identity
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * remove a EntityInstance
 *
 * @function remove
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EntityInstance
 */
/* jshint ignore:end */
EntityInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * fetch a EntityInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EntityInstance
 */
/* jshint ignore:end */
EntityInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Access the factors
 *
 * @function factors
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @returns {Twilio.Verify.V2.ServiceContext.EntityContext.FactorList}
 */
/* jshint ignore:end */
EntityInstance.prototype.factors = function factors() {
  return this._proxy.factors;
};

/* jshint ignore:start */
/**
 * Access the newFactors
 *
 * @function newFactors
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @returns {Twilio.Verify.V2.ServiceContext.EntityContext.NewFactorList}
 */
/* jshint ignore:end */
EntityInstance.prototype.newFactors = function newFactors() {
  return this._proxy.newFactors;
};

/* jshint ignore:start */
/**
 * Access the challenges
 *
 * @function challenges
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @returns {Twilio.Verify.V2.ServiceContext.EntityContext.ChallengeList}
 */
/* jshint ignore:end */
EntityInstance.prototype.challenges = function challenges() {
  return this._proxy.challenges;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.EntityInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
EntityInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EntityInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EntityContext
 *
 * @constructor Twilio.Verify.V2.ServiceContext.EntityContext
 *
 * @property {Twilio.Verify.V2.ServiceContext.EntityContext.FactorList} factors -
 *          factors resource
 * @property {Twilio.Verify.V2.ServiceContext.EntityContext.NewFactorList} newFactors -
 *          newFactors resource
 * @property {Twilio.Verify.V2.ServiceContext.EntityContext.ChallengeList} challenges -
 *          challenges resource
 *
 * @param {V2} version - Version of the resource
 * @param {sid} serviceSid - Service Sid.
 * @param {string} identity - Unique external identifier of the Entity
 */
/* jshint ignore:end */
EntityContext = function EntityContext(version, serviceSid, identity) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, identity: identity, };
  this._uri = `/Services/${serviceSid}/Entities/${identity}`;

  // Dependents
  this._factors = undefined;
  this._newFactors = undefined;
  this._challenges = undefined;
};

/* jshint ignore:start */
/**
 * remove a EntityInstance
 *
 * @function remove
 * @memberof Twilio.Verify.V2.ServiceContext.EntityContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EntityInstance
 */
/* jshint ignore:end */
EntityContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * fetch a EntityInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.ServiceContext.EntityContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EntityInstance
 */
/* jshint ignore:end */
EntityContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new EntityInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.identity
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(EntityContext.prototype,
  'factors', {
    get: function() {
      if (!this._factors) {
        this._factors = new FactorList(this._version, this._solution.serviceSid, this._solution.identity);
      }
      return this._factors;
    }
});

Object.defineProperty(EntityContext.prototype,
  'newFactors', {
    get: function() {
      if (!this._newFactors) {
        this._newFactors = new NewFactorList(
          this._version,
          this._solution.serviceSid,
          this._solution.identity
        );
      }
      return this._newFactors;
    }
});

Object.defineProperty(EntityContext.prototype,
  'challenges', {
    get: function() {
      if (!this._challenges) {
        this._challenges = new ChallengeList(
          this._version,
          this._solution.serviceSid,
          this._solution.identity
        );
      }
      return this._challenges;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.ServiceContext.EntityContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
EntityContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

EntityContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  EntityList: EntityList,
  EntityPage: EntityPage,
  EntityInstance: EntityInstance,
  EntityContext: EntityContext
};
