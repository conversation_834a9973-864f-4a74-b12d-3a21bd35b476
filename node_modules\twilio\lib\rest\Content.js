'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./content/V1');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize content domain
 *
 * @constructor Twilio.Content
 *
 * @property {Twilio.Content.V1} v1 - v1 version
 * @property {Twilio.Content.V1.ContentList} contents - contents resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Content(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://content.twilio.com');

  // Versions
  this._v1 = undefined;
}

_.extend(Content.prototype, Domain.prototype);
Content.prototype.constructor = Content;

Object.defineProperty(Content.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Content.prototype,
  'contents', {
    get: function() {
      return this.v1.contents;
    }
});

module.exports = Content;
