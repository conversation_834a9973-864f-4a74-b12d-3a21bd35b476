'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var InteractionChannelList = require(
    './interaction/interactionChannel').InteractionChannelList;
var Page = require('../../../base/Page');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var InteractionList;
var InteractionPage;
var InteractionInstance;
var InteractionContext;

/* jshint ignore:start */
/**
 * Initialize the InteractionList
 *
 * @constructor Twilio.FlexApi.V1.InteractionList
 *
 * @param {Twilio.FlexApi.V1} version - Version of the resource
 */
/* jshint ignore:end */
InteractionList = function InteractionList(version) {
  /* jshint ignore:start */
  /**
   * @function interaction
   * @memberof Twilio.FlexApi.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.FlexApi.V1.InteractionContext}
   */
  /* jshint ignore:end */
  function InteractionListInstance(sid) {
    return InteractionListInstance.get(sid);
  }

  InteractionListInstance._version = version;
  // Path Solution
  InteractionListInstance._solution = {};
  InteractionListInstance._uri = `/Interactions`;
  /* jshint ignore:start */
  /**
   * create a InteractionInstance
   *
   * @function create
   * @memberof Twilio.FlexApi.V1.InteractionList#
   *
   * @param {object} opts - Options for request
   * @param {object} opts.channel - The Interaction's channel
   * @param {object} opts.routing - The Interaction's routing logic
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed InteractionInstance
   */
  /* jshint ignore:end */
  InteractionListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['channel'])) {
      throw new Error('Required parameter "opts[\'channel\']" missing.');
    }
    if (_.isUndefined(opts['routing'])) {
      throw new Error('Required parameter "opts[\'routing\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'Channel': serialize.object(_.get(opts, 'channel')),
      'Routing': serialize.object(_.get(opts, 'routing'))
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new InteractionInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a interaction
   *
   * @function get
   * @memberof Twilio.FlexApi.V1.InteractionList#
   *
   * @param {string} sid - The SID that identifies the resource to fetch
   *
   * @returns {Twilio.FlexApi.V1.InteractionContext}
   */
  /* jshint ignore:end */
  InteractionListInstance.get = function get(sid) {
    return new InteractionContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.FlexApi.V1.InteractionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  InteractionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  InteractionListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return InteractionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the InteractionPage
 *
 * @constructor Twilio.FlexApi.V1.InteractionPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {InteractionSolution} solution - Path solution
 *
 * @returns InteractionPage
 */
/* jshint ignore:end */
InteractionPage = function InteractionPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(InteractionPage.prototype, Page.prototype);
InteractionPage.prototype.constructor = InteractionPage;

/* jshint ignore:start */
/**
 * Build an instance of InteractionInstance
 *
 * @function getInstance
 * @memberof Twilio.FlexApi.V1.InteractionPage#
 *
 * @param {InteractionPayload} payload - Payload response from the API
 *
 * @returns InteractionInstance
 */
/* jshint ignore:end */
InteractionPage.prototype.getInstance = function getInstance(payload) {
  return new InteractionInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionContext
 *
 * @constructor Twilio.FlexApi.V1.InteractionInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {object} channel - The Interaction's channel
 * @property {object} routing -
 *          A JSON Object representing the routing rules for the Interaction Channel
 * @property {string} url - The url
 * @property {string} links - The links
 *
 * @param {V1} version - Version of the resource
 * @param {InteractionPayload} payload - The instance payload
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
InteractionInstance = function InteractionInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.channel = payload.channel; // jshint ignore:line
  this.routing = payload.routing; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(InteractionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new InteractionContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a InteractionInstance
 *
 * @function fetch
 * @memberof Twilio.FlexApi.V1.InteractionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Access the channels
 *
 * @function channels
 * @memberof Twilio.FlexApi.V1.InteractionInstance#
 *
 * @returns {Twilio.FlexApi.V1.InteractionContext.InteractionChannelList}
 */
/* jshint ignore:end */
InteractionInstance.prototype.channels = function channels() {
  return this._proxy.channels;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

InteractionInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the InteractionContext
 *
 * @constructor Twilio.FlexApi.V1.InteractionContext
 *
 * @property {Twilio.FlexApi.V1.InteractionContext.InteractionChannelList} channels -
 *          channels resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
InteractionContext = function InteractionContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/Interactions/${sid}`;

  // Dependents
  this._channels = undefined;
};

/* jshint ignore:start */
/**
 * fetch a InteractionInstance
 *
 * @function fetch
 * @memberof Twilio.FlexApi.V1.InteractionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed InteractionInstance
 */
/* jshint ignore:end */
InteractionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new InteractionInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(InteractionContext.prototype,
  'channels', {
    get: function() {
      if (!this._channels) {
        this._channels = new InteractionChannelList(this._version, this._solution.sid);
      }
      return this._channels;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.InteractionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
InteractionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

InteractionContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  InteractionList: InteractionList,
  InteractionPage: InteractionPage,
  InteractionInstance: InteractionInstance,
  InteractionContext: InteractionContext
};
