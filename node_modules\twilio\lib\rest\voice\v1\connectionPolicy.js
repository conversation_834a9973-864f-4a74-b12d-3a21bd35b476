'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var ConnectionPolicyTargetList = require(
    './connectionPolicy/connectionPolicyTarget').ConnectionPolicyTargetList;
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var ConnectionPolicyList;
var ConnectionPolicyPage;
var ConnectionPolicyInstance;
var ConnectionPolicyContext;

/* jshint ignore:start */
/**
 * Initialize the ConnectionPolicyList
 *
 * @constructor Twilio.Voice.V1.ConnectionPolicyList
 *
 * @param {Twilio.Voice.V1} version - Version of the resource
 */
/* jshint ignore:end */
ConnectionPolicyList = function ConnectionPolicyList(version) {
  /* jshint ignore:start */
  /**
   * @function connectionPolicies
   * @memberof Twilio.Voice.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Voice.V1.ConnectionPolicyContext}
   */
  /* jshint ignore:end */
  function ConnectionPolicyListInstance(sid) {
    return ConnectionPolicyListInstance.get(sid);
  }

  ConnectionPolicyListInstance._version = version;
  // Path Solution
  ConnectionPolicyListInstance._solution = {};
  ConnectionPolicyListInstance._uri = `/ConnectionPolicies`;
  /* jshint ignore:start */
  /**
   * create a ConnectionPolicyInstance
   *
   * @function create
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.friendlyName] - A string to describe the resource
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed ConnectionPolicyInstance
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({'FriendlyName': _.get(opts, 'friendlyName')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ConnectionPolicyInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams ConnectionPolicyInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists ConnectionPolicyInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of ConnectionPolicyInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ConnectionPolicyPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of ConnectionPolicyInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new ConnectionPolicyPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a connection_policy
   *
   * @function get
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Voice.V1.ConnectionPolicyContext}
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.get = function get(sid) {
    return new ConnectionPolicyContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Voice.V1.ConnectionPolicyList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ConnectionPolicyListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ConnectionPolicyListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return ConnectionPolicyListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ConnectionPolicyPage
 *
 * @constructor Twilio.Voice.V1.ConnectionPolicyPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ConnectionPolicySolution} solution - Path solution
 *
 * @returns ConnectionPolicyPage
 */
/* jshint ignore:end */
ConnectionPolicyPage = function ConnectionPolicyPage(version, response,
                                                      solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ConnectionPolicyPage.prototype, Page.prototype);
ConnectionPolicyPage.prototype.constructor = ConnectionPolicyPage;

/* jshint ignore:start */
/**
 * Build an instance of ConnectionPolicyInstance
 *
 * @function getInstance
 * @memberof Twilio.Voice.V1.ConnectionPolicyPage#
 *
 * @param {ConnectionPolicyPayload} payload - Payload response from the API
 *
 * @returns ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyPage.prototype.getInstance = function getInstance(payload) {
  return new ConnectionPolicyInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ConnectionPolicyPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ConnectionPolicyPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ConnectionPolicyPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ConnectionPolicyContext
 *
 * @constructor Twilio.Voice.V1.ConnectionPolicyInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the resource
 * @property {string} links - The URLs of related resources
 *
 * @param {V1} version - Version of the resource
 * @param {ConnectionPolicyPayload} payload - The instance payload
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
ConnectionPolicyInstance = function ConnectionPolicyInstance(version, payload,
    sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(ConnectionPolicyInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ConnectionPolicyContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a ConnectionPolicyInstance
 *
 * @function fetch
 * @memberof Twilio.Voice.V1.ConnectionPolicyInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a ConnectionPolicyInstance
 *
 * @function update
 * @memberof Twilio.Voice.V1.ConnectionPolicyInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * remove a ConnectionPolicyInstance
 *
 * @function remove
 * @memberof Twilio.Voice.V1.ConnectionPolicyInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Access the targets
 *
 * @function targets
 * @memberof Twilio.Voice.V1.ConnectionPolicyInstance#
 *
 * @returns {Twilio.Voice.V1.ConnectionPolicyContext.ConnectionPolicyTargetList}
 */
/* jshint ignore:end */
ConnectionPolicyInstance.prototype.targets = function targets() {
  return this._proxy.targets;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ConnectionPolicyInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ConnectionPolicyInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ConnectionPolicyInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ConnectionPolicyContext
 *
 * @constructor Twilio.Voice.V1.ConnectionPolicyContext
 *
 * @property {Twilio.Voice.V1.ConnectionPolicyContext.ConnectionPolicyTargetList} targets -
 *          targets resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
ConnectionPolicyContext = function ConnectionPolicyContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/ConnectionPolicies/${sid}`;

  // Dependents
  this._targets = undefined;
};

/* jshint ignore:start */
/**
 * fetch a ConnectionPolicyInstance
 *
 * @function fetch
 * @memberof Twilio.Voice.V1.ConnectionPolicyContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new ConnectionPolicyInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a ConnectionPolicyInstance
 *
 * @function update
 * @memberof Twilio.Voice.V1.ConnectionPolicyContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({'FriendlyName': _.get(opts, 'friendlyName')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new ConnectionPolicyInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a ConnectionPolicyInstance
 *
 * @function remove
 * @memberof Twilio.Voice.V1.ConnectionPolicyContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ConnectionPolicyInstance
 */
/* jshint ignore:end */
ConnectionPolicyContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(ConnectionPolicyContext.prototype,
  'targets', {
    get: function() {
      if (!this._targets) {
        this._targets = new ConnectionPolicyTargetList(this._version, this._solution.sid);
      }
      return this._targets;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ConnectionPolicyContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ConnectionPolicyContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ConnectionPolicyContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ConnectionPolicyList: ConnectionPolicyList,
  ConnectionPolicyPage: ConnectionPolicyPage,
  ConnectionPolicyInstance: ConnectionPolicyInstance,
  ConnectionPolicyContext: ConnectionPolicyContext
};
