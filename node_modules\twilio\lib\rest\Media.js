'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./media/V1');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize media domain
 *
 * @constructor Twilio.Media
 *
 * @property {Twilio.Media.V1} v1 - v1 version
 * @property {Twilio.Media.V1.MediaProcessorList} mediaProcessor -
 *          mediaProcessor resource
 * @property {Twilio.Media.V1.MediaRecordingList} mediaRecording -
 *          mediaRecording resource
 * @property {Twilio.Media.V1.PlayerStreamerList} playerStreamer -
 *          playerStreamer resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Media(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://media.twilio.com');

  // Versions
  this._v1 = undefined;
}

_.extend(Media.prototype, Domain.prototype);
Media.prototype.constructor = Media;

Object.defineProperty(Media.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Media.prototype,
  'mediaProcessor', {
    get: function() {
      return this.v1.mediaProcessor;
    }
});

Object.defineProperty(Media.prototype,
  'mediaRecording', {
    get: function() {
      return this.v1.mediaRecording;
    }
});

Object.defineProperty(Media.prototype,
  'playerStreamer', {
    get: function() {
      return this.v1.playerStreamer;
    }
});

module.exports = Media;
