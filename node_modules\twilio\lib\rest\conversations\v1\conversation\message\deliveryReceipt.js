'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var DeliveryReceiptList;
var DeliveryReceiptPage;
var DeliveryReceiptInstance;
var DeliveryReceiptContext;

/* jshint ignore:start */
/**
 * Initialize the DeliveryReceiptList
 *
 * @constructor Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList
 *
 * @param {Twilio.Conversations.V1} version - Version of the resource
 * @param {string} conversationSid -
 *          The unique ID of the Conversation for this message.
 * @param {string} messageSid -
 *          The SID of the message the delivery receipt belongs to
 */
/* jshint ignore:end */
DeliveryReceiptList = function DeliveryReceiptList(version, conversationSid,
                                                    messageSid) {
  /* jshint ignore:start */
  /**
   * @function deliveryReceipts
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptContext}
   */
  /* jshint ignore:end */
  function DeliveryReceiptListInstance(sid) {
    return DeliveryReceiptListInstance.get(sid);
  }

  DeliveryReceiptListInstance._version = version;
  // Path Solution
  DeliveryReceiptListInstance._solution = {conversationSid: conversationSid, messageSid: messageSid};
  DeliveryReceiptListInstance._uri = `/Conversations/${conversationSid}/Messages/${messageSid}/Receipts`;
  /* jshint ignore:start */
  /**
   * Streams DeliveryReceiptInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists DeliveryReceiptInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of DeliveryReceiptInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new DeliveryReceiptPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of DeliveryReceiptInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new DeliveryReceiptPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a delivery_receipt
   *
   * @function get
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @param {string} sid -
   *          A 34 character string that uniquely identifies this resource.
   *
   * @returns {Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptContext}
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.get = function get(sid) {
    return new DeliveryReceiptContext(
      this._version,
      this._solution.conversationSid,
      this._solution.messageSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DeliveryReceiptListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DeliveryReceiptListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return DeliveryReceiptListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DeliveryReceiptPage
 *
 * @constructor Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DeliveryReceiptSolution} solution - Path solution
 *
 * @returns DeliveryReceiptPage
 */
/* jshint ignore:end */
DeliveryReceiptPage = function DeliveryReceiptPage(version, response, solution)
                                                    {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DeliveryReceiptPage.prototype, Page.prototype);
DeliveryReceiptPage.prototype.constructor = DeliveryReceiptPage;

/* jshint ignore:start */
/**
 * Build an instance of DeliveryReceiptInstance
 *
 * @function getInstance
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptPage#
 *
 * @param {DeliveryReceiptPayload} payload - Payload response from the API
 *
 * @returns DeliveryReceiptInstance
 */
/* jshint ignore:end */
DeliveryReceiptPage.prototype.getInstance = function getInstance(payload) {
  return new DeliveryReceiptInstance(
    this._version,
    payload,
    this._solution.conversationSid,
    this._solution.messageSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeliveryReceiptPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DeliveryReceiptPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeliveryReceiptContext
 *
 * @constructor Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptInstance
 *
 * @property {string} accountSid -
 *          The unique ID of the Account responsible for this participant.
 * @property {string} conversationSid -
 *          The unique ID of the Conversation for this message.
 * @property {string} sid -
 *          A 34 character string that uniquely identifies this resource.
 * @property {string} messageSid -
 *          The SID of the message the delivery receipt belongs to
 * @property {string} channelMessageSid -
 *          A messaging channel-specific identifier for the message delivered to participant
 * @property {string} participantSid -
 *          The unique ID of the participant the delivery receipt belongs to.
 * @property {delivery_receipt.delivery_status} status -
 *          The message delivery status
 * @property {number} errorCode -
 *          The message {@link https://www.twilio.com/docs/sms/api/message-resource#delivery-related-errors|delivery error code} for a `failed` status
 * @property {Date} dateCreated - The date that this resource was created.
 * @property {Date} dateUpdated - The date that this resource was last updated.
 * @property {string} url - An absolute URL for this delivery receipt.
 *
 * @param {V1} version - Version of the resource
 * @param {DeliveryReceiptPayload} payload - The instance payload
 * @param {sid} conversationSid -
 *          The unique ID of the Conversation for this message.
 * @param {sid} messageSid - The SID of the message the delivery receipt belongs to
 * @param {sid} sid - A 34 character string that uniquely identifies this resource.
 */
/* jshint ignore:end */
DeliveryReceiptInstance = function DeliveryReceiptInstance(version, payload,
                                                            conversationSid,
                                                            messageSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.conversationSid = payload.conversation_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.messageSid = payload.message_sid; // jshint ignore:line
  this.channelMessageSid = payload.channel_message_sid; // jshint ignore:line
  this.participantSid = payload.participant_sid; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.errorCode = deserialize.integer(payload.error_code); // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {conversationSid: conversationSid, messageSid: messageSid, sid: sid || this.sid, };
};

Object.defineProperty(DeliveryReceiptInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DeliveryReceiptContext(
          this._version,
          this._solution.conversationSid,
          this._solution.messageSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a DeliveryReceiptInstance
 *
 * @function fetch
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeliveryReceiptInstance
 */
/* jshint ignore:end */
DeliveryReceiptInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeliveryReceiptInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DeliveryReceiptInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeliveryReceiptContext
 *
 * @constructor Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} conversationSid -
 *          The unique ID of the Conversation for this delivery receipt.
 * @param {sid} messageSid -
 *          The SID of the message the delivery receipt belongs to.
 * @param {sid} sid - A 34 character string that uniquely identifies this resource.
 */
/* jshint ignore:end */
DeliveryReceiptContext = function DeliveryReceiptContext(version,
                                                          conversationSid,
                                                          messageSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {conversationSid: conversationSid, messageSid: messageSid, sid: sid, };
  this._uri = `/Conversations/${conversationSid}/Messages/${messageSid}/Receipts/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a DeliveryReceiptInstance
 *
 * @function fetch
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeliveryReceiptInstance
 */
/* jshint ignore:end */
DeliveryReceiptContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new DeliveryReceiptInstance(
      this._version,
      payload,
      this._solution.conversationSid,
      this._solution.messageSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Conversations.V1.ConversationContext.MessageContext.DeliveryReceiptContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeliveryReceiptContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DeliveryReceiptContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DeliveryReceiptList: DeliveryReceiptList,
  DeliveryReceiptPage: DeliveryReceiptPage,
  DeliveryReceiptInstance: DeliveryReceiptInstance,
  DeliveryReceiptContext: DeliveryReceiptContext
};
