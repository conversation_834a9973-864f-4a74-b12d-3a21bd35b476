'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var BrandVettingList;
var BrandVettingPage;
var BrandVettingInstance;
var BrandVettingContext;

/* jshint ignore:start */
/**
 * Initialize the BrandVettingList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList
 *
 * @param {Twilio.Messaging.V1} version - Version of the resource
 * @param {string} brandSid - A2P BrandRegistration Sid
 */
/* jshint ignore:end */
BrandVettingList = function BrandVettingList(version, brandSid) {
  /* jshint ignore:start */
  /**
   * @function brandVettings
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingContext}
   */
  /* jshint ignore:end */
  function BrandVettingListInstance(sid) {
    return BrandVettingListInstance.get(sid);
  }

  BrandVettingListInstance._version = version;
  // Path Solution
  BrandVettingListInstance._solution = {brandSid: brandSid};
  BrandVettingListInstance._uri = `/a2p/BrandRegistrations/${brandSid}/Vettings`;
  /* jshint ignore:start */
  /**
   * create a BrandVettingInstance
   *
   * @function create
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {object} opts - Options for request
   * @param {brand_vetting.vetting_provider} opts.vettingProvider -
   *          Third-party provider of the vettings to create
   * @param {string} [opts.vettingId] - The unique ID of the vetting
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed BrandVettingInstance
   */
  /* jshint ignore:end */
  BrandVettingListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['vettingProvider'])) {
      throw new Error('Required parameter "opts[\'vettingProvider\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'VettingProvider': _.get(opts, 'vettingProvider'),
      'VettingId': _.get(opts, 'vettingId')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BrandVettingInstance(
        this._version,
        payload,
        this._solution.brandSid,
        this._solution.brandVettingSid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams BrandVettingInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {object} [opts] - Options for request
   * @param {brand_vetting.vetting_provider} [opts.vettingProvider] -
   *          Third-party provider of the vettings to create
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  BrandVettingListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists BrandVettingInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {object} [opts] - Options for request
   * @param {brand_vetting.vetting_provider} [opts.vettingProvider] -
   *          Third-party provider of the vettings to create
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BrandVettingListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of BrandVettingInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {object} [opts] - Options for request
   * @param {brand_vetting.vetting_provider} [opts.vettingProvider] -
   *          Third-party provider of the vettings to create
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BrandVettingListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'VettingProvider': _.get(opts, 'vettingProvider'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BrandVettingPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of BrandVettingInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BrandVettingListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new BrandVettingPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a brand_vetting
   *
   * @function get
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @param {string} brandVettingSid - SID for third-party vetting record
   *
   * @returns {Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingContext}
   */
  /* jshint ignore:end */
  BrandVettingListInstance.get = function get(brandVettingSid) {
    return new BrandVettingContext(this._version, this._solution.brandSid, brandVettingSid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BrandVettingListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BrandVettingListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return BrandVettingListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BrandVettingPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BrandVettingSolution} solution - Path solution
 *
 * @returns BrandVettingPage
 */
/* jshint ignore:end */
BrandVettingPage = function BrandVettingPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BrandVettingPage.prototype, Page.prototype);
BrandVettingPage.prototype.constructor = BrandVettingPage;

/* jshint ignore:start */
/**
 * Build an instance of BrandVettingInstance
 *
 * @function getInstance
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingPage#
 *
 * @param {BrandVettingPayload} payload - Payload response from the API
 *
 * @returns BrandVettingInstance
 */
/* jshint ignore:end */
BrandVettingPage.prototype.getInstance = function getInstance(payload) {
  return new BrandVettingInstance(this._version, payload, this._solution.brandSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BrandVettingPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BrandVettingPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BrandVettingContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingInstance
 *
 * @property {string} accountSid - The SID of the Account that created the vetting
 * @property {string} brandSid - A2P BrandRegistration Sid
 * @property {string} brandVettingSid - SID for third-party vetting record
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {string} vettingId - The unique ID of the vetting
 * @property {string} vettingClass - The type of vetting
 * @property {string} vettingStatus - Status of vetting attempt
 * @property {brand_vetting.vetting_provider} vettingProvider -
 *          Third-party provider that has conducted the vetting
 * @property {string} url - The absolute URL of the Brand Vetting
 *
 * @param {V1} version - Version of the resource
 * @param {BrandVettingPayload} payload - The instance payload
 * @param {sid} brandSid - A2P BrandRegistration Sid
 * @param {sid} brandVettingSid - SID for third-party vetting record
 */
/* jshint ignore:end */
BrandVettingInstance = function BrandVettingInstance(version, payload, brandSid,
                                                      brandVettingSid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.brandSid = payload.brand_sid; // jshint ignore:line
  this.brandVettingSid = payload.brand_vetting_sid; // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.vettingId = payload.vetting_id; // jshint ignore:line
  this.vettingClass = payload.vetting_class; // jshint ignore:line
  this.vettingStatus = payload.vetting_status; // jshint ignore:line
  this.vettingProvider = payload.vetting_provider; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {brandSid: brandSid, brandVettingSid: brandVettingSid || this.brandVettingSid, };
};

Object.defineProperty(BrandVettingInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BrandVettingContext(
          this._version,
          this._solution.brandSid,
          this._solution.brandVettingSid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a BrandVettingInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BrandVettingInstance
 */
/* jshint ignore:end */
BrandVettingInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BrandVettingInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BrandVettingInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BrandVettingContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} brandSid - A2P BrandRegistration Sid
 * @param {sid} brandVettingSid - SID for third-party vetting record
 */
/* jshint ignore:end */
BrandVettingContext = function BrandVettingContext(version, brandSid,
                                                    brandVettingSid) {
  this._version = version;

  // Path Solution
  this._solution = {brandSid: brandSid, brandVettingSid: brandVettingSid, };
  this._uri = `/a2p/BrandRegistrations/${brandSid}/Vettings/${brandVettingSid}`;
};

/* jshint ignore:start */
/**
 * fetch a BrandVettingInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BrandVettingInstance
 */
/* jshint ignore:end */
BrandVettingContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BrandVettingInstance(
      this._version,
      payload,
      this._solution.brandSid,
      this._solution.brandVettingSid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.BrandRegistrationContext.BrandVettingContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BrandVettingContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BrandVettingContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BrandVettingList: BrandVettingList,
  BrandVettingPage: BrandVettingPage,
  BrandVettingInstance: BrandVettingInstance,
  BrandVettingContext: BrandVettingContext
};
