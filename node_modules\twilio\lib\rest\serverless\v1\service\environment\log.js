'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require(
    '../../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var LogList;
var LogPage;
var LogInstance;
var LogContext;

/* jshint ignore:start */
/**
 * Initialize the LogList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList
 *
 * @param {Twilio.Serverless.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the Log resource is associated with
 * @param {string} environmentSid -
 *          The SID of the environment in which the log occurred
 */
/* jshint ignore:end */
LogList = function LogList(version, serviceSid, environmentSid) {
  /* jshint ignore:start */
  /**
   * @function logs
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogContext}
   */
  /* jshint ignore:end */
  function LogListInstance(sid) {
    return LogListInstance.get(sid);
  }

  LogListInstance._version = version;
  // Path Solution
  LogListInstance._solution = {serviceSid: serviceSid, environmentSid: environmentSid};
  LogListInstance._uri = `/Services/${serviceSid}/Environments/${environmentSid}/Logs`;
  /* jshint ignore:start */
  /**
   * Streams LogInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.functionSid] -
   *          The SID of the function whose invocation produced the Log resources to read
   * @param {Date} [opts.startDate] -
   *          The date and time after which the Log resources must have been created.
   * @param {Date} [opts.endDate] -
   *          The date and time before which the Log resource must have been created.
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  LogListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists LogInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.functionSid] -
   *          The SID of the function whose invocation produced the Log resources to read
   * @param {Date} [opts.startDate] -
   *          The date and time after which the Log resources must have been created.
   * @param {Date} [opts.endDate] -
   *          The date and time before which the Log resource must have been created.
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  LogListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of LogInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.functionSid] -
   *          The SID of the function whose invocation produced the Log resources to read
   * @param {Date} [opts.startDate] -
   *          The date and time after which the Log resources must have been created.
   * @param {Date} [opts.endDate] -
   *          The date and time before which the Log resource must have been created.
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  LogListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'FunctionSid': _.get(opts, 'functionSid'),
      'StartDate': serialize.iso8601DateTime(_.get(opts, 'startDate')),
      'EndDate': serialize.iso8601DateTime(_.get(opts, 'endDate')),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new LogPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of LogInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  LogListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new LogPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a log
   *
   * @function get
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @param {string} sid - The SID that identifies the Log resource to fetch
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogContext}
   */
  /* jshint ignore:end */
  LogListInstance.get = function get(sid) {
    return new LogContext(this._version, this._solution.serviceSid, this._solution.environmentSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  LogListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  LogListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return LogListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the LogPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {LogSolution} solution - Path solution
 *
 * @returns LogPage
 */
/* jshint ignore:end */
LogPage = function LogPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(LogPage.prototype, Page.prototype);
LogPage.prototype.constructor = LogPage;

/* jshint ignore:start */
/**
 * Build an instance of LogInstance
 *
 * @function getInstance
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogPage#
 *
 * @param {LogPayload} payload - Payload response from the API
 *
 * @returns LogInstance
 */
/* jshint ignore:end */
LogPage.prototype.getInstance = function getInstance(payload) {
  return new LogInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.environmentSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
LogPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

LogPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the LogContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogInstance
 *
 * @property {string} sid - The unique string that identifies the Log resource
 * @property {string} accountSid -
 *          The SID of the Account that created the Log resource
 * @property {string} serviceSid -
 *          The SID of the Service that the Log resource is associated with
 * @property {string} environmentSid -
 *          The SID of the environment in which the log occurred
 * @property {string} buildSid - The SID of the build that corresponds to the log
 * @property {string} deploymentSid -
 *          The SID of the deployment that corresponds to the log
 * @property {string} functionSid -
 *          The SID of the function whose invocation produced the log
 * @property {string} requestSid - The SID of the request associated with the log
 * @property {log.level} level - The log level
 * @property {string} message - The log message
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the Log resource was created
 * @property {string} url - The absolute URL of the Log resource
 *
 * @param {V1} version - Version of the resource
 * @param {LogPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the Log resource is associated with
 * @param {sid} environmentSid -
 *          The SID of the environment in which the log occurred
 * @param {sid} sid - The SID that identifies the Log resource to fetch
 */
/* jshint ignore:end */
LogInstance = function LogInstance(version, payload, serviceSid, environmentSid,
                                    sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.environmentSid = payload.environment_sid; // jshint ignore:line
  this.buildSid = payload.build_sid; // jshint ignore:line
  this.deploymentSid = payload.deployment_sid; // jshint ignore:line
  this.functionSid = payload.function_sid; // jshint ignore:line
  this.requestSid = payload.request_sid; // jshint ignore:line
  this.level = payload.level; // jshint ignore:line
  this.message = payload.message; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, environmentSid: environmentSid, sid: sid || this.sid, };
};

Object.defineProperty(LogInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new LogContext(
          this._version,
          this._solution.serviceSid,
          this._solution.environmentSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a LogInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed LogInstance
 */
/* jshint ignore:end */
LogInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
LogInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

LogInstance.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the LogContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Service to fetch the Log resource from
 * @param {sid} environmentSid -
 *          The SID of the environment with the Log resource to fetch
 * @param {sid} sid - The SID that identifies the Log resource to fetch
 */
/* jshint ignore:end */
LogContext = function LogContext(version, serviceSid, environmentSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, environmentSid: environmentSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Environments/${environmentSid}/Logs/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a LogInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed LogInstance
 */
/* jshint ignore:end */
LogContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new LogInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.environmentSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.EnvironmentContext.LogContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
LogContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

LogContext.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  LogList: LogList,
  LogPage: LogPage,
  LogInstance: LogInstance,
  LogContext: LogContext
};
