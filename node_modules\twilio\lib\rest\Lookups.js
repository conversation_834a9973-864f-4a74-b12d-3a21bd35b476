'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./lookups/V1');  /* jshint ignore:line */
var V2 = require('./lookups/V2');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize lookups domain
 *
 * @constructor Twilio.Lookups
 *
 * @property {Twilio.Lookups.V1} v1 - v1 version
 * @property {Twilio.Lookups.V2} v2 - v2 version
 * @property {Twilio.Lookups.V1.PhoneNumberList} phoneNumbers -
 *          phoneNumbers resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Lookups(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://lookups.twilio.com');

  // Versions
  this._v1 = undefined;
  this._v2 = undefined;
}

_.extend(Lookups.prototype, Domain.prototype);
Lookups.prototype.constructor = Lookups;

Object.defineProperty(Lookups.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Lookups.prototype,
  'v2', {
    get: function() {
      this._v2 = this._v2 || new V2(this);
      return this._v2;
    }
});

Object.defineProperty(Lookups.prototype,
  'phoneNumbers', {
    get: function() {
      return this.v1.phoneNumbers;
    }
});

module.exports = Lookups;
