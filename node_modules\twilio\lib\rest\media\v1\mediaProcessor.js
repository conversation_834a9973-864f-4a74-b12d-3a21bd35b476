'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var MediaProcessorList;
var MediaProcessorPage;
var MediaProcessorInstance;
var MediaProcessorContext;

/* jshint ignore:start */
/**
 * Initialize the MediaProcessorList
 *
 * @constructor Twilio.Media.V1.MediaProcessorList
 *
 * @param {Twilio.Media.V1} version - Version of the resource
 */
/* jshint ignore:end */
MediaProcessorList = function MediaProcessorList(version) {
  /* jshint ignore:start */
  /**
   * @function mediaProcessor
   * @memberof Twilio.Media.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Media.V1.MediaProcessorContext}
   */
  /* jshint ignore:end */
  function MediaProcessorListInstance(sid) {
    return MediaProcessorListInstance.get(sid);
  }

  MediaProcessorListInstance._version = version;
  // Path Solution
  MediaProcessorListInstance._solution = {};
  MediaProcessorListInstance._uri = `/MediaProcessors`;
  /* jshint ignore:start */
  /**
   * create a MediaProcessorInstance
   *
   * @function create
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.extension - The Media Extension name or URL
   * @param {string} opts.extensionContext - The Media Extension context
   * @param {object} [opts.extensionEnvironment] - The Media Extension environment
   * @param {string} [opts.statusCallback] -
   *          The URL to send MediaProcessor event updates to your application
   * @param {string} [opts.statusCallbackMethod] -
   *          The HTTP method Twilio should use to call the `status_callback` URL
   * @param {number} [opts.maxDuration] - Maximum MediaProcessor duration in minutes
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed MediaProcessorInstance
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['extension'])) {
      throw new Error('Required parameter "opts[\'extension\']" missing.');
    }
    if (_.isUndefined(opts['extensionContext'])) {
      throw new Error('Required parameter "opts[\'extensionContext\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'Extension': _.get(opts, 'extension'),
      'ExtensionContext': _.get(opts, 'extensionContext'),
      'ExtensionEnvironment': serialize.object(_.get(opts, 'extensionEnvironment')),
      'StatusCallback': _.get(opts, 'statusCallback'),
      'StatusCallbackMethod': _.get(opts, 'statusCallbackMethod'),
      'MaxDuration': _.get(opts, 'maxDuration')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new MediaProcessorInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams MediaProcessorInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {object} [opts] - Options for request
   * @param {media_processor.order} [opts.order] - The sort order of the list
   * @param {media_processor.status} [opts.status] - Status to filter by
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists MediaProcessorInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {object} [opts] - Options for request
   * @param {media_processor.order} [opts.order] - The sort order of the list
   * @param {media_processor.status} [opts.status] - Status to filter by
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of MediaProcessorInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {object} [opts] - Options for request
   * @param {media_processor.order} [opts.order] - The sort order of the list
   * @param {media_processor.status} [opts.status] - Status to filter by
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'Order': _.get(opts, 'order'),
      'Status': _.get(opts, 'status'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new MediaProcessorPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of MediaProcessorInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new MediaProcessorPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a media_processor
   *
   * @function get
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @param {string} sid - The SID that identifies the resource to fetch
   *
   * @returns {Twilio.Media.V1.MediaProcessorContext}
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.get = function get(sid) {
    return new MediaProcessorContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Media.V1.MediaProcessorList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  MediaProcessorListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  MediaProcessorListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return MediaProcessorListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the MediaProcessorPage
 *
 * @constructor Twilio.Media.V1.MediaProcessorPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {MediaProcessorSolution} solution - Path solution
 *
 * @returns MediaProcessorPage
 */
/* jshint ignore:end */
MediaProcessorPage = function MediaProcessorPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(MediaProcessorPage.prototype, Page.prototype);
MediaProcessorPage.prototype.constructor = MediaProcessorPage;

/* jshint ignore:start */
/**
 * Build an instance of MediaProcessorInstance
 *
 * @function getInstance
 * @memberof Twilio.Media.V1.MediaProcessorPage#
 *
 * @param {MediaProcessorPayload} payload - Payload response from the API
 *
 * @returns MediaProcessorInstance
 */
/* jshint ignore:end */
MediaProcessorPage.prototype.getInstance = function getInstance(payload) {
  return new MediaProcessorInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Media.V1.MediaProcessorPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
MediaProcessorPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

MediaProcessorPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the MediaProcessorContext
 *
 * @constructor Twilio.Media.V1.MediaProcessorInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} sid - The unique string that identifies the resource
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} extension - The Media Extension name or URL
 * @property {string} extensionContext - The Media Extension context
 * @property {media_processor.status} status - The status of the MediaProcessor
 * @property {string} url - The absolute URL of the resource
 * @property {string} endedReason - The reason why a MediaProcessor ended
 * @property {string} statusCallback -
 *          The URL to which Twilio will send MediaProcessor event updates
 * @property {string} statusCallbackMethod -
 *          The HTTP method Twilio should use to call the `status_callback` URL
 * @property {number} maxDuration - Maximum MediaProcessor duration in seconds
 *
 * @param {V1} version - Version of the resource
 * @param {MediaProcessorPayload} payload - The instance payload
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
MediaProcessorInstance = function MediaProcessorInstance(version, payload, sid)
                                                          {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.extension = payload.extension; // jshint ignore:line
  this.extensionContext = payload.extension_context; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.endedReason = payload.ended_reason; // jshint ignore:line
  this.statusCallback = payload.status_callback; // jshint ignore:line
  this.statusCallbackMethod = payload.status_callback_method; // jshint ignore:line
  this.maxDuration = deserialize.integer(payload.max_duration); // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(MediaProcessorInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new MediaProcessorContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a MediaProcessorInstance
 *
 * @function fetch
 * @memberof Twilio.Media.V1.MediaProcessorInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MediaProcessorInstance
 */
/* jshint ignore:end */
MediaProcessorInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a MediaProcessorInstance
 *
 * @function update
 * @memberof Twilio.Media.V1.MediaProcessorInstance#
 *
 * @param {object} opts - Options for request
 * @param {media_processor.update_status} opts.status -
 *          The status of the MediaProcessor
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MediaProcessorInstance
 */
/* jshint ignore:end */
MediaProcessorInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Media.V1.MediaProcessorInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
MediaProcessorInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

MediaProcessorInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the MediaProcessorContext
 *
 * @constructor Twilio.Media.V1.MediaProcessorContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
MediaProcessorContext = function MediaProcessorContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/MediaProcessors/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a MediaProcessorInstance
 *
 * @function fetch
 * @memberof Twilio.Media.V1.MediaProcessorContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MediaProcessorInstance
 */
/* jshint ignore:end */
MediaProcessorContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new MediaProcessorInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a MediaProcessorInstance
 *
 * @function update
 * @memberof Twilio.Media.V1.MediaProcessorContext#
 *
 * @param {object} opts - Options for request
 * @param {media_processor.update_status} opts.status -
 *          The status of the MediaProcessor
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MediaProcessorInstance
 */
/* jshint ignore:end */
MediaProcessorContext.prototype.update = function update(opts, callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['status'])) {
    throw new Error('Required parameter "opts[\'status\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({'Status': _.get(opts, 'status')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new MediaProcessorInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Media.V1.MediaProcessorContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
MediaProcessorContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

MediaProcessorContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  MediaProcessorList: MediaProcessorList,
  MediaProcessorPage: MediaProcessorPage,
  MediaProcessorInstance: MediaProcessorInstance,
  MediaProcessorContext: MediaProcessorContext
};
