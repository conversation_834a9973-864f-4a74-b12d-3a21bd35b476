'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var DayList = require('./export/day').DayList;
var ExportCustomJobList = require(
    './export/exportCustomJob').ExportCustomJobList;
var JobList = require('./export/job').JobList;
var Page = require('../../../base/Page');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var ExportList;
var ExportPage;
var ExportInstance;
var ExportContext;

/* jshint ignore:start */
/**
 * Initialize the ExportList
 *
 * @constructor Twilio.Bulkexports.V1.ExportList
 *
 * @param {Twilio.Bulkexports.V1} version - Version of the resource
 */
/* jshint ignore:end */
ExportList = function ExportList(version) {
  /* jshint ignore:start */
  /**
   * @function exports
   * @memberof Twilio.Bulkexports.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Bulkexports.V1.ExportContext}
   */
  /* jshint ignore:end */
  function ExportListInstance(sid) {
    return ExportListInstance.get(sid);
  }

  ExportListInstance._version = version;
  // Path Solution
  ExportListInstance._solution = {};

  // Components
  ExportListInstance._jobs = undefined;

  /* jshint ignore:start */
  /**
   * Constructs a export
   *
   * @function get
   * @memberof Twilio.Bulkexports.V1.ExportList#
   *
   * @param {string} resourceType -
   *          The type of communication – Messages, Calls, Conferences, and Participants
   *
   * @returns {Twilio.Bulkexports.V1.ExportContext}
   */
  /* jshint ignore:end */
  ExportListInstance.get = function get(resourceType) {
    return new ExportContext(this._version, resourceType);
  };

  Object.defineProperty(ExportListInstance,
    'jobs', {
      get: function jobs() {
        if (!this._jobs) {
          this._jobs = new JobList(this._version);
        }

        return this._jobs;
      }
  });

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Bulkexports.V1.ExportList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ExportListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ExportListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return ExportListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ExportPage
 *
 * @constructor Twilio.Bulkexports.V1.ExportPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ExportSolution} solution - Path solution
 *
 * @returns ExportPage
 */
/* jshint ignore:end */
ExportPage = function ExportPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ExportPage.prototype, Page.prototype);
ExportPage.prototype.constructor = ExportPage;

/* jshint ignore:start */
/**
 * Build an instance of ExportInstance
 *
 * @function getInstance
 * @memberof Twilio.Bulkexports.V1.ExportPage#
 *
 * @param {ExportPayload} payload - Payload response from the API
 *
 * @returns ExportInstance
 */
/* jshint ignore:end */
ExportPage.prototype.getInstance = function getInstance(payload) {
  return new ExportInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Bulkexports.V1.ExportPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExportPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExportPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExportContext
 *
 * @constructor Twilio.Bulkexports.V1.ExportInstance
 *
 * @property {string} resourceType -
 *          The type of communication – Messages, Calls, Conferences, and Participants
 * @property {string} url - The URL of this resource.
 * @property {string} links - Nested resource URLs.
 *
 * @param {V1} version - Version of the resource
 * @param {ExportPayload} payload - The instance payload
 * @param {string} resourceType -
 *          The type of communication – Messages, Calls, Conferences, and Participants
 */
/* jshint ignore:end */
ExportInstance = function ExportInstance(version, payload, resourceType) {
  this._version = version;

  // Marshaled Properties
  this.resourceType = payload.resource_type; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {resourceType: resourceType || this.resourceType, };
};

Object.defineProperty(ExportInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ExportContext(this._version, this._solution.resourceType);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a ExportInstance
 *
 * @function fetch
 * @memberof Twilio.Bulkexports.V1.ExportInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExportInstance
 */
/* jshint ignore:end */
ExportInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Access the days
 *
 * @function days
 * @memberof Twilio.Bulkexports.V1.ExportInstance#
 *
 * @returns {Twilio.Bulkexports.V1.ExportContext.DayList}
 */
/* jshint ignore:end */
ExportInstance.prototype.days = function days() {
  return this._proxy.days;
};

/* jshint ignore:start */
/**
 * Access the exportCustomJobs
 *
 * @function exportCustomJobs
 * @memberof Twilio.Bulkexports.V1.ExportInstance#
 *
 * @returns {Twilio.Bulkexports.V1.ExportContext.ExportCustomJobList}
 */
/* jshint ignore:end */
ExportInstance.prototype.exportCustomJobs = function exportCustomJobs() {
  return this._proxy.exportCustomJobs;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Bulkexports.V1.ExportInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExportInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExportInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExportContext
 *
 * @constructor Twilio.Bulkexports.V1.ExportContext
 *
 * @property {Twilio.Bulkexports.V1.ExportContext.DayList} days - days resource
 * @property {Twilio.Bulkexports.V1.ExportContext.ExportCustomJobList} exportCustomJobs -
 *          exportCustomJobs resource
 *
 * @param {V1} version - Version of the resource
 * @param {string} resourceType -
 *          The type of communication – Messages, Calls, Conferences, and Participants
 */
/* jshint ignore:end */
ExportContext = function ExportContext(version, resourceType) {
  this._version = version;

  // Path Solution
  this._solution = {resourceType: resourceType, };
  this._uri = `/Exports/${resourceType}`;

  // Dependents
  this._days = undefined;
  this._exportCustomJobs = undefined;
};

/* jshint ignore:start */
/**
 * fetch a ExportInstance
 *
 * @function fetch
 * @memberof Twilio.Bulkexports.V1.ExportContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExportInstance
 */
/* jshint ignore:end */
ExportContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new ExportInstance(this._version, payload, this._solution.resourceType));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(ExportContext.prototype,
  'days', {
    get: function() {
      if (!this._days) {
        this._days = new DayList(this._version, this._solution.resourceType);
      }
      return this._days;
    }
});

Object.defineProperty(ExportContext.prototype,
  'exportCustomJobs', {
    get: function() {
      if (!this._exportCustomJobs) {
        this._exportCustomJobs = new ExportCustomJobList(this._version, this._solution.resourceType);
      }
      return this._exportCustomJobs;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Bulkexports.V1.ExportContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExportContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ExportContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ExportList: ExportList,
  ExportPage: ExportPage,
  ExportInstance: ExportInstance,
  ExportContext: ExportContext
};
