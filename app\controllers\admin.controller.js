//Required External Modules
const bcrypt = require('bcrypt');
const jwt = require("jsonwebtoken");
path = require('path');
//admin External Files
const Admin = require("../models/admin.models.js");
const Mail = require("../models/mail.js");
const Permission = require('./../models/CMS-Model/permission.models.js');

// Handle create admin actions
exports.signUp = (req, res) => {

    //validation request
    if (!req.body.email || !req.body.password) {
        return res.status(400).send({
            message: "Required field can not be empty",
        });
    }
    //Create a admin
    const admin = new Admin({
        name: req.body.name,
        email: req.body.email,
        password: bcrypt.hashSync(req.body.password, 10)
    });

    //Save admin to database
    admin.save()
        .then((data) => {
            res.status(200).send({ 'code': 200, 'data': data });
        })
        .catch((err) => {
            res.status(200).send({
                'code': 100,
                message: err.message || "Some error occurred while creating the admin.",
            });
        });
};

//admin login usin email and password
exports.signIn = async (req, res) => {
    if (!req.body.email)
        return res.status(400).send({ 'code': 100, message: "Required field can't empty." });
    //find email in "admins" collections
    let admin = await Admin.findOne({ email: req.body.email }).populate('role_id');
    //Email doesn't exist in the admins collections
    if (!admin) {
        return res.status(400).send({ 'code': 100, message: "This Email is not registered with us." });
    }
    //check Employee can change password
    if (admin.status == false || admin.role_id.acc_activation == false)
        return res.status(400).send({ 'code': 100, message: "Please, Contact the Admin." });
    let isMatch = false;
    let crendial = 'password';
    if (req.body.password)
        //Email exist in admins collections to match the Decrypted password
        isMatch = await bcrypt.compare(req.body.password, admin.password);
    if (req.body.pin) {
        //Email exist in admins collections to match the Decrypted password
        isMatch = await bcrypt.compare(req.body.pin, admin.pin);
        crendial = 'pin';
    }
    //Password doesn't match return response
    if (!isMatch) {
        return res.status(400).send({ 'code': 100, message: "Your " + crendial + " is incorrect" })
    }
    const payload = { admin: { id: admin.id } };

    //Create json web token
    const token = jwt.sign(payload, "TOPSECRETE", { expiresIn: '365d' });

    admin.tokens = token;
    const data = await Admin.updateOne({ _id: admin._id }, { tokens: token }, { new: true })
    return res.status(200).send({ 'code': 200, data: admin, message: 'Login successfully' });
};

exports.forgotPassword = async (req, res, next) => {
    console.log("called", req.body )
    if (!req.body.email)
        return res.status(400).send({ 'code': 100, message: "Required field can't empty." });

    let admin = await Admin.findOne({ email: req.body.email })
    if (!admin)
        return res.status(400).send({ 'code': 100, message: "This Email is not registered with us" });
    const payload = { admin: { id: admin._id } };
    //Create json web token
    const token = jwt.sign(payload, "randomString", { expiresIn: 3600 });

    admin.resetPasswordToken = token;

    //const tokens = token;
    const result = await admin.save();
    var data = {
        email: req.body.email,
        token: token,
        name: admin.name
    }
    Mail.adminforgotPassword(data, function (err1, res1) {});
    if (result)
        return res.status(200).send({ 'code': 200, message: "Password reset link sent to your mail,please check.." });
};

//Update device token
exports.updateDeviceToken = async (req, res) => {
    if (!req.body.user_id)
        return res.status(400).send({ 'code': 100, message: "Required field can't empty." });
    
    const data = await Admin.updateOne({ _id: req.body.user_id }, { device_token: req.body.device_token }, { new: true })
    return res.status(200).send({ 'code': 200, message: 'Successfully updated.' });
};


