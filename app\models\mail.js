const nodemailer = require("nodemailer");
const sgMail = require('@sendgrid/mail');
const fs = require("fs");
const moment = require('moment');
const tz = require('moment-timezone');
const stripe = require('stripe')(process.env.SECRET_kEY);
const User = require("../models/user.models"); 
const Order = require('../models/order.models');
const mycart = require('../models/mycart.models')
const Variants = require('../models/product_variant');
const Location = require('../models/CMS-Model/location.models.js');
const { base64encode, base64decode } = require('nodejs-base64');
const QRCode = require('qrcode');
const PUSHNOT = require("../../app/middleware/push_notifiction");

sgMail.setApiKey(process.env.SEND_GRID_KEY);
const transporter = nodemailer.createTransport({
	service: 'Gmail',
	auth: {
		user: process.env.EMAIL,
		pass: process.env.PASSWORD
	}
});

const Mail = function (user) { };

Mail.GetPrimaryStripeCard = async (data,result) => {
	if (!data.customer_id)
        return res.status(200).send({ code: 100, message: 'Field content can`t empty!!!.' }); 
    const Data = await User.findOne({ _id: data.customer_id });

    // const cards = await stripe.customers.listSources(req.params.customer, { object: 'card', limit: 3 });
    let paymentMethods = []; 
     
    if (Data.stripe_id != null)
        try{
            paymentMethods = await stripe.paymentMethods.list({
                customer: Data.stripe_id,
                type: 'card',
            });
        } catch (e) {

            return -1;
        }
    var rss = (Data.stripe_id != null)? paymentMethods.data:[]; 
    console.log("payment card==========================");
    // console.log(Data);
    // console.log(paymentMethods);
    console.log("payment card 1");
    // console.log(rss);
    if(rss.length>0&&Data.primary_card_id!=null)
    {
        var tempIndex=rss.findIndex((item)=>item.id===Data.primary_card_id)
        if(tempIndex!=-1)
        {
            var tempItem={...rss[tempIndex]}
            rss.splice(tempIndex,1)
            rss.splice(0,0,tempItem)
        }
    } 
    console.log(rss[0]);
    if (Data)
	 return rss[0];
	else
	 return 0;
}

Mail.orderPayment = async (data,result) => {
	if (!data.email || !data.user_id || !data.card_id || !data.total  || !data.process_id)
        return -1;
	var indata=data;
	const Orderdetails = await Order.findOne({ _id: indata.process_id });
	Orderdetails.token = base64encode(Orderdetails.order_id);
	// Converting the data into String format
	let stringdata = JSON.stringify(Orderdetails.token);

		 
    stripe.paymentIntents.create({
        amount: Math.round(data.total * 100),// Math.round(data.total),
        currency: 'USD',
        payment_method: data.card_id,
        payment_method_types: ['card'],
        customer: data.customer_id,
        confirm: true,
        metadata: {
            email: data.email,
            process: 'shop',
            process_id: "'"+data.process_id+"'",
            total: data.total,
            user_id: "'"+data.user_id+"'"
        }
    }, async (err, res1) => {
		 
		var qrcode = await QRCode.toDataURL(stringdata, { type: 'terminal' });
		const user = await User.findOne({ "_id": data.user_id });
		const Odetails = await Order.findOne({ _id: data.process_id }).populate('product.product_id');
		var client_email = user.email!=null?user.email:""
    	var emailData ={ clientname:user.first_name, clientemail:client_email}   
        if (err) {
			var updateData =
                {
                    approved:1,
					payment_status:2,
					pickuplocation:null,
					location:null,
					status:3,
					delivery_date:null,
                    reason: err.raw.message
                }  
			const updatedata = await Order.findByIdAndUpdate(data.process_id, updateData, { new: true });
			if (user.notification_status == true) {
				

				console.log(err);
				// var msg = {
				// 	"to": user.device_token,
				// 	"notification": {
				// 		"title": "Your order has been declined. Here is why",
				// 		"body": err.raw.message,
				// 		"content_available": true,
				// 		"priority": "high"
				// 	},
				// 	"data": {
				// 		"title": "Reed Animal",
				// 		"user_id": data.user_id,
				// 		"screen_name": "order",
				// 		"order_id": data.orderid,
				// 		"content_available": true,
				// 		"priority": "high",
				// 		"type": "profile"
				// 	}
				// }


                  var msg = {
                    "tokens": [user.device_token],
                    "mutable_content": true,
                    "data":  {
                    "title": "Reed Animal",
                    // "body":`These are prescription items, they are subject to approval. You will only be charged once your order is approved.`,
                    "user_id": data.user_id.toString(),
                    "screen_name": "order",
                    "order_id": data.order_id.toString(),
                    "content_available": "true",
                    "priority": "high",
                    "type": "profile"
                },

                    notification:{},
                    android:{
                         notification:{
                          title:"Your order has been declined. Here is why",
                          body:err.raw.message,
                          sound : "default",
                         },
                    },

                    apns:{
                      payload:{
                        aps:{
                         alert:{
                          title:"Your order has been declined. Here is why",
                          body: err.raw.message,
                         },
                         sound : "default"

                        }
                      }

                    },
                    webpush : {} ,
                    fcmOptions:{},
                  }
				var Not = PUSHNOT.Notifi(msg, function (err1, res2) { })
			}
	
			let subject = "Your order has been declined"
			let title = "Your order has been declined. Here is why"
			let sub_title = err.raw.message           
			Mail.orderDeclineEmail(Odetails,emailData,title,sub_title,subject , function (err2, res2) { });
              
            return 0;
        } else {

			
			// console.log(res1);
			console.log("notification order payment success=============================>1")
                var USER = await User.findOne({ "_id": indata.user_id });
                const Order_details = await Order.findOne({ _id: indata.process_id });
                

               
				console.log("QR Code: ---->");
				console.log(qrcode);
                Order_details.qrcode = { qrcode };
                Order_details.payment_completed = true;
				Order_details.payment_status =1;
                Order_details.payment_details = res1;
				console.log("Order details: ---->");
				console.log(Order_details);
                const data = await Order_details.save();

				console.log("Order details: ---->save");
				console.log(data);
                if (indata.delete_cart != null && indata.delete_cart)
                    await mycart.deleteMany({ user_id: indata.user_id });

					
                if (USER.notification_status == true) {
                    // var msg = {
                    //     "to": [USER.device_token],
                    //     "notification": {
                    //         "title": "Reed Animal",
                    //         "body": `Your order is ready for pick up.`,
                    //         "content_available": true,
                    //         "priority": "high"
                    //     },
                    //     "data": {
                    //         "title": "Reed Animal",
                    //         "user_id": indata.user_id,
                    //         "screen_name": "order",
                    //         "order_id": indata.orderid,
                    //         "content_available": true,
                    //         "priority": "high",
                    //         "type": "profile"
                    //     }
                    // }


                  var msg = {
                    "tokens": [USER.device_token],
                    "mutable_content": true,
                    "data":  {
	                    "title": "Reed Animal",
	                    // "body":`These are prescription items, they are subject to approval. You will only be charged once your order is approved.`,
	                    "user_id": indata.user_id.toString(),
	                    "screen_name": "order",
	                    "order_id": indata.orderid.toString(),
	                    "content_available": "true",
	                    "priority": "high",
	                    "type": "profile"
	                },

                    notification:{},
                    android:{
                         notification:{
                          title:"Reed Animal",
                          body: `Your order is ready for pick up.`,
                          sound : "default",
                         },
                    },

                    apns:{
                      payload:{
                        aps:{
                         alert:{
                          title:"Reed Animal",
                          body: `Your order is ready for pick up.`,
                         },
                         sound : "default"

                        }
                      }

                    },
                    webpush : {} ,
                    fcmOptions:{},
                 }
				
                    //Push Notification to the user
                    var Not = PUSHNOT.Notifi(msg, function (err1, res2) { })
                }

                // const Odetails1 = await Order.findOne({ _id: indata.process_id }).populate('product.product_id');
                var client_email = USER.email!=null?USER.email:""
                var emailData =
                {
                    clientname:USER.first_name,
                    clientemail:client_email
                }                
				var pickuploc=await Location.findOne({ '_id': Odetails.pickuplocation });
				Odetails.location=pickuploc.name;
				 
                await Mail.sendOrderedMail(Odetails,emailData, function (err2, res2) { });
                await Mail.sendOrderedMailToOwner(Odetails, emailData, function (err2, res2) { });        
            

            return 1;
        }
    });
}

Mail.adminforgotPassword = (data, result) => {

	var htmlTemplate = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'/><title>Dr.Reed</title><meta name='viewport' content='width=device-width, initial-scale=1.0'/></head><body style='margin: 0; padding: 0;'><div><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css'></div><table border='0' cellpadding='0' cellspacing='0' width='100%'> <tr><td style='padding: 10px 0 30px 0;'><table align='center' border='0' cellpadding='0' cellspacing='0' width='600' style='border: 1px solid #cccccc; border-collapse: collapse;'><tr><td align='center' width='100%' height='50' style='padding: 13px 0 15px 0;color: #153643;font-size: 28px;font-weight: bold;font-family: Arial, sans-serif;position: relative;background: #d5eac3;border-bottom: 1px solid #cccccc;'><img src='http://**************/assets/img/brand/email_template_logo.png' alt='Logo'/></td></tr><tr><td bgcolor='#ffffff' style='padding: 40px 30px 40px 30px;'><table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #153643; font-family: Arial, sans-serif; font-size: 16px;'> Hi " + data.name + ",</td></tr><tr><td style='padding: 20px 0 30px 0; color: #153643; font-family: Arial, sans-serif; font-size: 16px; line-height: 20px;'><div><p>We received a request to reset your password.</p><p>Click the button below to change your password.</p></div></td></tr><tr><td style='text-align: center;'><a href='" + process.env.URL + "/#/reset-password?token=" + data.token + "' style='background-color:#568d2f;border-radius:4px;color:#ffffff;display:inline-block;font-family:Helvetica,Arial,sans-serif;font-size:14px;font-weight:600;line-height:42px;text-align:center;text-decoration:none;width:280px;padding: 10px 10px 10px 10px;'>Reset password</a></td></tr></table> </td></tr><tr><td bgcolor='#8FAADC' style='background:#e8e8e8;padding: 10px 30px 10px 30px;'> <table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #000000; font-family: Arial, sans-serif; font-size: 14px;opacity: 0.3;' width='75%'>© All Rights Reserved 2021<br/></td><td align='right' width='25%'><table border='0' cellpadding='0' cellspacing='0'></table></td></tr></table></td></tr></table></td></tr></table></body></html>"

	let mailOptions = {
		from: process.env.MAILS,
		to: data.email,
		subject: 'Reg: Reed Animal Hospital Password reset link',
		html: htmlTemplate,
	};

	// transporter.sendMail(mailOptions, function (err, data) {
	// 	if (error) {
	// 		result(error, null);
	// 	} else {
	// 		result(null, response);
	// 	}
	// });
	console.log("sgMail", sgMail, mailOptions)
	sgMail.send(mailOptions).then((response) => {
		console.log('Email sent');
		console.log(response);   // 👈 Log SendGrid's response

	}).catch((error) => {
		console.error(error);
	});
}

Mail.forgotPassword = (data, result) => {

	var htmlTemplate = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'/><title>Dr.Reed</title><meta name='viewport' content='width=device-width, initial-scale=1.0'/></head><body style='margin: 0; padding: 0;'><div><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css'></div><table border='0' cellpadding='0' cellspacing='0' width='100%'> <tr><td style='padding: 10px 0 30px 0;'><table align='center' border='0' cellpadding='0' cellspacing='0' width='600' style='border: 1px solid #cccccc; border-collapse: collapse;'><tr><td align='center' width='100%' height='50' style='padding: 13px 0 15px 0;color: #153643;font-size: 28px;font-weight: bold;font-family: Arial, sans-serif;position: relative;background: #d5eac3;border-bottom: 1px solid #cccccc;'><img src='http://**************/assets/img/brand/email_template_logo.png' alt='Logo'/></td></tr><tr><td bgcolor='#ffffff' style='padding: 40px 30px 40px 30px;'><table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #153643; font-family: Arial, sans-serif; font-size: 16px;'> Hi " + data.name + ",</td></tr><tr><td style='padding: 20px 0 30px 0; color: #153643; font-family: Arial, sans-serif; font-size: 16px; line-height: 20px;'><div><p>We received a request to reset your password.</p><p>Click the button below to change your password.</p></div></td></tr><tr><td style='text-align: center;'><a href='" + process.env.URL + "/#/app-reset-password?token=" + data.token + "' style='background-color:#568d2f;border-radius:4px;color:#ffffff;display:inline-block;font-family:Helvetica,Arial,sans-serif;font-size:14px;font-weight:600;line-height:42px;text-align:center;text-decoration:none;width:280px;padding: 10px 10px 10px 10px;'>Reset password</a></td></tr></table> </td></tr><tr><td bgcolor='#8FAADC' style='background:#e8e8e8;padding: 10px 30px 10px 30px;'> <table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #000000; font-family: Arial, sans-serif; font-size: 14px;opacity: 0.3;' width='75%'>© All Rights Reserved 2021<br/></td><td align='right' width='25%'><table border='0' cellpadding='0' cellspacing='0'></table></td></tr></table></td></tr></table></td></tr></table></body></html>"
	let mailOptions = {
		from: process.env.MAILS,
		to: data.email,
		subject: 'Reg: Reed Animal Hospital Password reset link',
		// text: `Click the below link\n         
		// ${process.env.URL}/#/app-reset-password?token=${data.token}`
		html: htmlTemplate,
	};

	// transporter.sendMail(mailOptions, function (err, data) {
	// 	if (error) {
	// 		result(error, null);
	// 	} else {
	// 		result(null, response);
	// 	}
	// });
	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}

Mail.InvitationMail = (data, res) => {

	var htmlTemplate = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'/><title>Dr.Reed</title><meta name='viewport' content='width=device-width, initial-scale=1.0'/></head><body style='margin: 0; padding: 0;'><div><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css'></div><table border='0' cellpadding='0' cellspacing='0' width='100%'> <tr><td style='padding: 10px 0 30px 0;'><table align='center' border='0' cellpadding='0' cellspacing='0' width='600' style='border: 1px solid #cccccc; border-collapse: collapse;'><tr><td align='center' width='100%' height='50' style='padding: 13px 0 15px 0;color: #153643;font-size: 28px;font-weight: bold;font-family: Arial, sans-serif;position: relative;background: #d5eac3;border-bottom: 1px solid #cccccc;'><img src='http://**************/assets/img/brand/email_template_logo.png' alt='Logo'/></td></tr><tr><td bgcolor='#ffffff' style='padding: 40px 30px 40px 30px;'><table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #153643; font-family: Arial, sans-serif; font-size: 16px;'> Hi " + data.name + ",</td></tr><tr><td style='padding: 20px 0 30px 0; color: #153643; font-family: Arial, sans-serif; font-size: 16px; line-height: 20px;'><div><p>You are just one step away from completing your registration.</p><p>Click on the button below to verify your email & activate your account.</p></div></td></tr><tr><td style='text-align: center;'><a href='" + process.env.BASEURL + "/activation/" + data.tokens + "' style='background-color:#568d2f;border-radius:4px;color:#ffffff;display:inline-block;font-family:Helvetica,Arial,sans-serif;font-size:14px;font-weight:600;line-height:42px;text-align:center;text-decoration:none;width:280px;padding: 10px 10px 10px 10px;'>Activate Account</a></td></tr></table> </td></tr><tr><td bgcolor='#8FAADC' style='background:#e8e8e8;padding: 10px 30px 10px 30px;'> <table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #000000; font-family: Arial, sans-serif; font-size: 14px;opacity: 0.3;' width='75%'>© All Rights Reserved 2021<br/></td><td align='right' width='25%'><table border='0' cellpadding='0' cellspacing='0'></table></td></tr></table></td></tr></table></td></tr></table></body></html>"

	const mailOptions = {
		from: process.env.MAILS,
		to: data.email,
		subject: 'Reg: Reed Animal Hospital Account Activation link',
		html: htmlTemplate
		// text: `Click the below link\n         
		// ${process.env.BASEURL}/activation/${data.tokens}`
	};

	// transporter.sendMail(mailOptions, function (err, data) {
	// 	if (err) {
	// 		// console.log('hi',err)
	// 		return err
	// 	} else {
	// 		return res.send(data);
	// 	}
	// });
	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}

//Employee confirm
Mail.EmployeeComfirm = (data, result) => {

	var htmlTemplate = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'/><title>Dr.Reed</title><meta name='viewport' content='width=device-width, initial-scale=1.0'/></head><body style='margin: 0; padding: 0;'><div><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css'></div><table border='0' cellpadding='0' cellspacing='0' width='100%'> <tr><td style='padding: 10px 0 30px 0;'><table align='center' border='0' cellpadding='0' cellspacing='0' width='600' style='border: 1px solid #cccccc; border-collapse: collapse;'><tr><td align='left' width='100%' height='50' style='padding: 13px 15px 15px 15px;color: #153643;font-size: 28px;font-weight: bold;font-family: Arial, sans-serif;position: relative;background: #d5eac3;border-bottom: 1px solid #cccccc;'><img width='250px' src='http://**************/assets/img/brand/email_template_logo.png' alt='Logo'/></td></tr><tr><td bgcolor='#ffffff' style='padding: 40px 30px 40px 30px;'><table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #153643; font-family: Arial, sans-serif; font-size: 16px;'> Hi " + data.name + ",</td></tr><tr><td style='padding: 20px 0 30px 0; color: #153643; font-family: Arial, sans-serif; font-size: 16px; line-height: 20px;'><div><p>You are just one step away from your Dr.Reed registration before you can start using.</p><p>Click on the button below to verify your email & set new password.</p></div></td></tr><tr><td style='text-align: center;'><a href='" + process.env.URL + "/#/reset-password?token=" + data.tokens + "' style='background-color:#568d2f;border-radius:4px;color:#ffffff;display:inline-block;font-family:Helvetica,Arial,sans-serif;font-size:14px;font-weight:600;line-height:42px;text-align:center;text-decoration:none;width:280px;padding: 10px 10px 10px 10px;'>Set password</a></td></tr></table> </td></tr><tr><td bgcolor='#8FAADC' style='background:#e8e8e8;padding: 10px 30px 10px 30px;'> <table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #000000; font-family: Arial, sans-serif; font-size: 14px;opacity: 0.3;' width='75%'>© All Rights Reserved 2021<br/></td><td align='right' width='25%'><table border='0' cellpadding='0' cellspacing='0'></table></td></tr></table></td></tr></table></td></tr></table></body></html>"
	let msg = {
		from: process.env.MAILS,
		to: data.email,
		subject: 'Reg: Reed Animal Hospital Set New Password link',
		// text: `Click the below link\n         
		// ${process.env.URL}/#/reset-password?token=${data.tokens}`
		html: htmlTemplate,
	};
	sgMail.send(msg)
	.then(() => {
		console.log('Email sent')
	})
	.catch((error) => {
		console.error('errr-->',error)
	})
	// transporter.sendMail(mailOptions, function (err, data) {
	// 	if (error) {
	// 		result(error, null);
	// 	} else {
	// 		result(null, response);
	// 	}
	// })
}
// Appointment Email

Mail.AppointmentConfirmation = (data, res) => {
	var temp_AppointmentType=data.appointment_type=="Video"?"Video Appointment":"Appoinment"
	var temp_bottomMsg=data.appointment_type=="Video"?"Join your session using Reed Animal Hospital App.":'in '+data.location+', Reed Animal Hospital.'
	var temp_Header=data.fromReschedule!=null&&data.fromReschedule?"Rescheduled":data.fromCancellation!=null&&data.fromCancellation?"Cancelled":"Confirmed"
	var showbottomMsg=data.appointment_type=="Video"&&data.fromCancellation!=null&&data.fromCancellation?"":temp_bottomMsg;
	if(temp_Header=="Confirmed"){
		temp_Header =data.appointment_type=="Video"?"Confirmed":"Requested";
	}	
	var htmlTemplate = '<html data-editor-version="2" class="sg-campaigns"> <head> <meta http-equiv="Content-Type" content="text/html; charset=utf-8"> <meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"> <meta http-equiv="X-UA-Compatible" content="IE=Edge"> <style type="text/css"> body, p, div{font-family: lucida sans unicode,lucida grande,sans-serif; font-size: 14px;}body{color: #666666;}body a{color: #0FBA85; text-decoration: none;}p{margin: 0; padding: 0;}table.wrapper{width:100% !important; table-layout: fixed; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: 100%; -moz-text-size-adjust: 100%; -ms-text-size-adjust: 100%;}img.max-width{max-width: 100% !important;}.column.of-2{width: 50%;}.column.of-3{width: 33.333%;}.column.of-4{width: 25%;}ul ul ul ul{list-style-type: disc !important;}ol ol{list-style-type: lower-roman !important;}ol ol ol{list-style-type: lower-latin !important;}ol ol ol ol{list-style-type: decimal !important;}@media screen and (max-width:480px){.preheader .rightColumnContent, .footer .rightColumnContent{text-align: left !important;}.preheader .rightColumnContent div, .preheader .rightColumnContent span, .footer .rightColumnContent div, .footer .rightColumnContent span{text-align: left !important;}.preheader .rightColumnContent, .preheader .leftColumnContent{font-size: 80% !important; padding: 5px 0;}table.wrapper-mobile{width: 100% !important; table-layout: fixed;}img.max-width{height: auto !important; max-width: 100% !important;}a.bulletproof-button{display: block !important; width: auto !important; font-size: 80%; padding-left: 0 !important; padding-right: 0 !important;}.columns{width: 100% !important;}.column{display: block !important; width: 100% !important; padding-left: 0 !important; padding-right: 0 !important; margin-left: 0 !important; margin-right: 0 !important;}.social-icon-column{display: inline-block !important;}}</style> </head> <body> <center class="wrapper" data-link-color="#0FBA85" data-body-style="font-size:14px; font-family:lucida sans unicode,lucida grande,sans-serif; color:#666666; background-color:#CCCCCC;"> <div class="webkit"> <table cellpadding="0" cellspacing="0" border="0" width="100%" class="wrapper" bgcolor="#CCCCCC"> <tr> <td valign="top" bgcolor="#CCCCCC" width="100%"> <table width="100%" role="content-container" class="outer" align="center" cellpadding="0" cellspacing="0" border="0"> <tr> <td width="100%"> <table width="100%" cellpadding="0" cellspacing="0" border="0"> <tr> <td> <table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width:600px;" align="center"> <tr> <td role="modules-container" style="padding:0px 0px 0px 0px; color:#666666; text-align:left;" bgcolor="#CCCCCC" width="100%" align="left"><table class="module preheader preheader-hide" role="module" data-type="preheader" border="0" cellpadding="0" cellspacing="0" width="100%" style="display: none !important; mso-hide: all; visibility: hidden; opacity: 0; color: transparent; height: 0; width: 0;"> <tr> <td role="module-content"> <p></p></td></tr></table> <table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:20px 80px 10px 80px;" bgcolor="" data-distribution="1"> <tbody> <tr role="module-content"> <td height="100%" valign="top"><table width="140" style="width:140px; border-spacing:0; border-collapse:collapse; margin:0px 150px 0px 150px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"> <tbody> <tr> <td style="padding:0px;margin:0px;border-spacing:0;"><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center"> <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="140" alt="" data-proportionally-constrained="true" data-responsive="true" src="https://reedapp.net/assets/img/brand/email_template_logo.png"> </td></tr></tbody> </table></td></tr></tbody> </table></td></tr></tbody> </table><table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:50px 0px 40px 0px;" bgcolor="#FFFFFF" data-distribution="1"> <tbody> <tr role="module-content"> <td height="100%" valign="top"><table width="560" style="width:560px; border-spacing:0; border-collapse:collapse; margin:0px 20px 0px 20px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"> <tbody> <tr> <td style="padding:0px;margin:0px;border-spacing:0;"><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td style="padding:0px 0px 0px 0px; line-height:44px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif; color: #568d2c; font-size: 44px">Appointment</span></div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif; color: #568d2c; font-size: 44px">'+temp_Header+'</span></div><div></div></div></td></tr></tbody> </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td style="padding:30px 0px 18px 0px; line-height:18px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif">Hello '+data.clientname+',</span></div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif">Your '+temp_AppointmentType+' with '+data.doctorname+' is</span></div><div></div></div></td></tr></tbody> </table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;"> <tbody> <tr> <td style="padding:18px 0px 0px 0px; line-height:18px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif"> on '+data.day+', '+data.date+' at '+data.time+'</span></div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif">'+showbottomMsg+'</span></div><div></div></div></td></tr></tbody> </table></td></tr></tbody> </table></td></tr></tbody> </table><table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:30px 10px 40px 10px;" bgcolor="#568d2c" data-distribution="1"> <tbody> <tr role="module-content"> <td height="100%" valign="top"><table width="420" style="width:420px; border-spacing:0; border-collapse:collapse; margin:0px 80px 0px 80px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"> <tbody> <tr> <td style="padding:0px;margin:0px;border-spacing:0;"><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td style="padding:0px 0px 0px 0px; line-height:20px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: inherit"><span style="font-size: 14px; color: #fff">Please do not reply to this email</span></div><div style="font-family: inherit; text-align: inherit"></div><div style="font-family: inherit; text-align: inherit; margin-left: 0px"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-weight: inherit; font-stretch: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; color: #fff; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 14px">Warmly,</span></div><div style="font-family: inherit; text-align: inherit; margin-left: 0px"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-weight: inherit; font-stretch: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; color: #fff; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 14px">The Reed Animal Hospital Team</span></div><div></div></div></td></tr></tbody> </table></td></tr></tbody> </table></td></tr></tbody> </table><table class="module" role="module" data-type="spacer" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td style="padding:0px 0px 30px 0px;" role="module-content" bgcolor=""> </td></tr></tbody> </table><table class="module" role="module" data-type="social" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" > <tbody> <tr> <td valign="top" style="padding:0px 0px 0px 0px; font-size:6px; line-height:10px;" align="center"> <table align="center" style="-webkit-margin-start:auto;-webkit-margin-end:auto;"> <tbody><tr align="center"><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.facebook.com/reedanimalhosp" target="_blank" alt="Facebook" title="Facebook" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Facebook" title="Facebook" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Ffacebook.png?alt=media&token=e8038c52-ffc5-4bcd-9c51-39ca1f278441" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://twitter.com/reedanimalhosp" target="_blank" alt="Twitter" title="Twitter" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Twitter" title="Twitter" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Ftwitter.png?alt=media&token=4ede41e6-ecf3-45b5-a2df-ba5591877ccb" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.instagram.com/reedanimalhosp/" target="_blank" alt="Instagram" title="Instagram" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Instagram" title="Instagram" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Finstagram.png?alt=media&token=0cdce8ce-503e-495d-8e15-8f6fe27a2754" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.pinterest.com/ReedAnimalHosp/" target="_blank" alt="Pinterest" title="Pinterest" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Pinterest" title="Pinterest" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Fpinterest.png?alt=media&token=f1d9c64d-b162-442e-9201-37b7da544dfa" style="height:21px; width:21px;" height="21" width="21"> </a> </td></tr></tbody> </table> </td></tr></tbody> </table></td></tr></table> </td></tr></table> </td></tr></table> </td></tr></table> </div></center> </body> </html>'
	var to_email=[]
	if(data.clientemail.length>0){
		to_email=[data.clientemail,data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	   //to_email=[data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	}
	else{
		to_email=[data.clientemail,data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	    //to_email=[data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]	
	}
	const mailOptions = {
		from: process.env.MAILS,
		to: to_email,
		subject: 'Reed Animal Hospital Appointment Details',
		html: htmlTemplate
		// text: `Click the below link\n         
		// ${process.env.BASEURL}/activation/${data.tokens}`
	};
	
	sgMail.sendMultiple(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}

Mail.AppointmentCancellation = (data, res) => {
	var temp_AppointmentType=data.appointment_type=="Video"?"Video Appointment":"Appoinment"
	var temp_bottomMsg=data.appointment_type=="Video"?"Join your session using Reed Animal Hospital App.":'in '+data.location+', Reed Animal Hospital.'
	var temp_Header=data.fromReschedule!=null&&data.fromReschedule?"Rescheduled":data.fromCancellation!=null&&data.fromCancellation?"Cancelled":"Confirmed"
	var showbottomMsg=data.appointment_type=="Video"&&data.fromCancellation!=null&&data.fromCancellation?"":temp_bottomMsg;
	if(temp_Header=="Confirmed"){
		temp_Header =data.appointment_type=="Video"?"Confirmed":"Requested";
	}	
	var htmlTemplate = '<html data-editor-version="2" class="sg-campaigns"> <head><meta http-equiv="Content-Type" content="text/html; charset=utf-8"><meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1, maximum-scale=1"> <meta http-equiv="X-UA-Compatible" content="IE=Edge"> <style type="text/css"> body, p, div{font-family: lucida sans unicode,lucida grande,sans-serif; font-size: 14px;}body{color: #666666;}body a{color: #0FBA85; text-decoration: none;}p{margin: 0; padding: 0;}table.wrapper{width:100% !important; table-layout: fixed; -webkit-font-smoothing: antialiased; -webkit-text-size-adjust: 100%; -moz-text-size-adjust: 100%; -ms-text-size-adjust: 100%;}img.max-width{max-width: 100% !important;}.column.of-2{width: 50%;}.column.of-3{width: 33.333%;}.column.of-4{width: 25%;}ul ul ul ul{list-style-type: disc !important;}ol ol{list-style-type: lower-roman !important;}ol ol ol{list-style-type: lower-latin !important;}ol ol ol ol{list-style-type: decimal !important;}@media screen and (max-width:480px){.preheader .rightColumnContent, .footer .rightColumnContent{text-align: left !important;}.preheader .rightColumnContent div, .preheader .rightColumnContent span, .footer .rightColumnContent div, .footer .rightColumnContent span{text-align: left !important;}.preheader .rightColumnContent, .preheader .leftColumnContent{font-size: 80% !important; padding: 5px 0;}table.wrapper-mobile{width: 100% !important; table-layout: fixed;}img.max-width{height: auto !important; max-width: 100% !important;}a.bulletproof-button{display: block !important; width: auto !important; font-size: 80%; padding-left: 0 !important; padding-right: 0 !important;}.columns{width: 100% !important;}.column{display: block !important; width: 100% !important; padding-left: 0 !important; padding-right: 0 !important; margin-left: 0 !important; margin-right: 0 !important;}.social-icon-column{display: inline-block !important;}}</style> </head> <body><center class="wrapper" data-link-color="#0FBA85" data-body-style="font-size:14px; font-family:lucida sans unicode,lucida grande,sans-serif; color:#666666; background-color:#CCCCCC;"> <div class="webkit"> <table cellpadding="0" cellspacing="0" border="0" width="100%" class="wrapper" bgcolor="#CCCCCC"><tr><td valign="top" bgcolor="#CCCCCC" width="100%"><table width="100%" role="content-container" class="outer" align="center" cellpadding="0" cellspacing="0" border="0"><tr><td width="100%"><table width="100%" cellpadding="0" cellspacing="0" border="0"><tr><td><table width="100%" cellpadding="0" cellspacing="0" border="0" style="width:100%; max-width:600px;" align="center"><tr><td role="modules-container" style="padding:0px 0px 0px 0px; color:#666666; text-align:left;" bgcolor="#CCCCCC" width="100%" align="left"><table class="module preheader preheader-hide" role="module" data-type="preheader" border="0" cellpadding="0" cellspacing="0" width="100%" style="display: none !important; mso-hide: all; visibility: hidden; opacity: 0; color: transparent; height: 0; width: 0;"><tr><td role="module-content"><p></p></td></tr></table><table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:20px 80px 10px 80px;" bgcolor="" data-distribution="1"><tbody><tr role="module-content"><td height="100%" valign="top"><table width="140" style="width:140px; border-spacing:0; border-collapse:collapse; margin:0px 150px 0px 150px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"><tbody><tr><td style="padding:0px;margin:0px;border-spacing:0;"><table class="wrapper" role="module" data-type="image" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td style="font-size:6px; line-height:10px; padding:0px 0px 0px 0px;" valign="top" align="center"> <img class="max-width" border="0" style="display:block; color:#000000; text-decoration:none; font-family:Helvetica, arial, sans-serif; font-size:16px; max-width:100% !important; width:100%; height:auto !important;" width="140" alt="" data-proportionally-constrained="true" data-responsive="true" src="https://reedapp.net/assets/img/brand/email_template_logo.png"> </td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:50px 0px 40px 0px;" bgcolor="#FFFFFF" data-distribution="1"><tbody><tr role="module-content"><td height="100%" valign="top"><table width="560" style="width:560px; border-spacing:0; border-collapse:collapse; margin:0px 20px 0px 20px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"><tbody><tr><td style="padding:0px;margin:0px;border-spacing:0;"><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td style="padding:0px 0px 0px 0px; line-height:44px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif; color: #568d2c; font-size: 44px">Appointment</span></div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif; color: #568d2c; font-size: 44px">'+temp_Header+'</span></div><div></div></div></td></tr></tbody></table><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td style="padding:30px 0px 18px 0px; line-height:18px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif">Hello '+data.clientname+',</span></div><div style="font-family: inherit; text-align: center"><span style="font-family: lucida sans unicode, lucida grande, sans-serif">Your appointment with '+data.doctorname+' on '+data.date+' at '+data.time+' at '+data.location+' Reed Animal Hospital has been cancelled.</span></div><div></div></div></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><table border="0" cellpadding="0" cellspacing="0" align="center" width="100%" role="module" data-type="columns" style="padding:30px 10px 40px 10px;" bgcolor="#568d2c" data-distribution="1"><tbody><tr role="module-content"><td height="100%" valign="top"><table width="420" style="width:420px; border-spacing:0; border-collapse:collapse; margin:0px 80px 0px 80px;" cellpadding="0" cellspacing="0" align="left" border="0" bgcolor="" class="column column-0"><tbody><tr><td style="padding:0px;margin:0px;border-spacing:0;"><table class="module" role="module" data-type="text" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td style="padding:0px 0px 0px 0px; line-height:20px; text-align:inherit;" height="100%" valign="top" bgcolor="" role="module-content"><div><div style="font-family: inherit; text-align: inherit"><span style="font-size: 14px; color: #fff">Please do not reply to this email.</span></div><div style="font-family: inherit; text-align: inherit"></div><div style="font-family: inherit; text-align: inherit; margin-left: 0px"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-weight: inherit; font-stretch: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; color: #fff; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 14px">Warmly,</span></div><div style="font-family: inherit; text-align: inherit; margin-left: 0px"><span style="box-sizing: border-box; padding-top: 0px; padding-right: 0px; padding-bottom: 0px; padding-left: 0px; margin-top: 0px; margin-right: 0px; margin-bottom: 0px; margin-left: 0px; font-style: inherit; font-variant-ligatures: inherit; font-variant-caps: inherit; font-variant-numeric: inherit; font-variant-east-asian: inherit; font-weight: inherit; font-stretch: inherit; line-height: inherit; font-family: inherit; vertical-align: baseline; border-top-width: 0px; border-right-width: 0px; border-bottom-width: 0px; border-left-width: 0px; border-top-style: initial; border-right-style: initial; border-bottom-style: initial; border-left-style: initial; border-top-color: initial; border-right-color: initial; border-bottom-color: initial; border-left-color: initial; border-image-source: initial; border-image-slice: initial; border-image-width: initial; border-image-outset: initial; border-image-repeat: initial; color: #fff; letter-spacing: normal; orphans: 2; text-align: start; text-indent: 0px; text-transform: none; white-space: pre-wrap; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 14px">The Reed Animal Hospital Team</span></div><div></div></div></td></tr></tbody></table></td></tr></tbody></table></td></tr></tbody></table><table class="module" role="module" data-type="spacer" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td style="padding:0px 0px 30px 0px;" role="module-content" bgcolor=""> </td></tr></tbody></table><table class="module" role="module" data-type="social" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="table-layout: fixed;" ><tbody><tr><td valign="top" style="padding:0px 0px 0px 0px; font-size:6px; line-height:10px;" align="center"><table align="center" style="-webkit-margin-start:auto;-webkit-margin-end:auto;"><tbody><tr align="center"><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.facebook.com/reedanimalhosp" target="_blank" alt="Facebook" title="Facebook" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Facebook" title="Facebook" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Ffacebook.png?alt=media&token=e8038c52-ffc5-4bcd-9c51-39ca1f278441" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://twitter.com/reedanimalhosp" target="_blank" alt="Twitter" title="Twitter" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Twitter" title="Twitter" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Ftwitter.png?alt=media&token=4ede41e6-ecf3-45b5-a2df-ba5591877ccb" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.instagram.com/reedanimalhosp/" target="_blank" alt="Instagram" title="Instagram" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Instagram" title="Instagram" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Finstagram.png?alt=media&token=0cdce8ce-503e-495d-8e15-8f6fe27a2754" style="height:21px; width:21px;" height="21" width="21"> </a> </td><td style="padding: 0px 5px;" class="social-icon-column"> <a role="social-icon-link" href="https://www.pinterest.com/ReedAnimalHosp/" target="_blank" alt="Pinterest" title="Pinterest" style="display:inline-block; background-color:#568d2C; height:21px; width:21px;"> <img role="social-icon" alt="Pinterest" title="Pinterest" src="https://firebasestorage.googleapis.com/v0/b/giftedchat-006.appspot.com/o/emailtemplate%2Fpinterest.png?alt=media&token=f1d9c64d-b162-442e-9201-37b7da544dfa" style="height:21px; width:21px;" height="21" width="21"> </a> </td></tr></tbody></table></td></tr></tbody></table></td></tr></table></td></tr></table></td></tr></table></td></tr></table></div></center></body></html>'
	var to_email=[]
	if(data.clientemail.length>0){
		to_email=[data.clientemail,data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	   //to_email=[data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	}
	else{
		to_email=[data.clientemail,data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]
	    //to_email=[data.location=="Saratoga"?"<EMAIL>":"<EMAIL>"]	
	}
	const mailOptions = {
		from: process.env.MAILS,
		to: to_email,
		subject: 'Reed Animal Hospital Appointment Details',
		html: htmlTemplate
		// text: `Click the below link\n         
		// ${process.env.BASEURL}/activation/${data.tokens}`
	};
	
	sgMail.sendMultiple(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}




Mail.sendPdf = (data,dat, res) => {

	var htmlTemplate = "<html><head><meta http-equiv='Content-Type' content='text/html; charset=UTF-8'/><title>Dr.Reed</title><meta name='viewport' content='width=device-width, initial-scale=1.0'/></head><body style='margin: 0; padding: 0;'><div><link rel='stylesheet' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css'></div><table border='0' cellpadding='0' cellspacing='0' width='100%'> <tr><td style='padding: 10px 0 30px 0;'><table align='center' border='0' cellpadding='0' cellspacing='0' width='600' style='border: 1px solid #cccccc; border-collapse: collapse;'><tr><td align='center' width='100%' height='50' style='padding: 13px 0 15px 0;color: #153643;font-size: 28px;font-weight: bold;font-family: Arial, sans-serif;position: relative;background: #d5eac3;border-bottom: 1px solid #cccccc;'><img src='http://**************/assets/img/brand/email_template_logo.png' alt='Logo'/></td></tr><tr><td bgcolor='#ffffff' style='padding: 40px 30px 40px 30px;'><table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #153643; font-family: Arial, sans-serif; font-size: 16px;'> Hi " + data.name + ",</td></tr><tr><td style='padding: 20px 0 30px 0; color: #153643; font-family: Arial, sans-serif; font-size: 16px; line-height: 20px;'><div><p></p><p>Thank you, attached a summary of your Services/Prescriptions list for the appointment you booked on "+dat.date+" "+dat.time+"  with "+dat.doctor_name+" at "+dat.location+".</p></div></td></tr><tr><td style='text-align: center;'></td></tr></table> </td></tr><tr><td bgcolor='#8FAADC' style='background:#e8e8e8;padding: 10px 30px 10px 30px;'> <table border='0' cellpadding='0' cellspacing='0' width='100%'><tr><td style='color: #000000; font-family: Arial, sans-serif; font-size: 14px;opacity: 0.3;' width='75%'>© All Rights Reserved 2021<br/></td><td align='right' width='25%'><table border='0' cellpadding='0' cellspacing='0'></table></td></tr></table></td></tr></table></td></tr></table></body></html>"

	pathToAttachment = data.filepath;
	attachment = fs.readFileSync(pathToAttachment).toString("base64");
	const mailOptions = {
		from: process.env.MAILS,
		to: data.email,
		subject: 'Services/Prescriptions Summary',
		html: htmlTemplate,
		attachments: [
		{
			content: attachment,
			filename: data.filename,
			type: "application/pdf",
			disposition: "attachment"
		}
		]
	};

	sgMail.sendMultiple(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}

Mail.orderInProgressMail = (data, user, result) => {
	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center>Hello '+user.clientname+'!</h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>We’ve received your order.</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"><div><p>These are prescription items, they are subject to approval. You will only be charged once your order is approved. Once your items are ready for pickup we will send you an email. Please note that the pickup location may differ from where your clinic is located. The pickup address will be in your pickup email.<p>You can also view your order in the mobile app by going to your profile and clicking on orders.</div></table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''
	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){
		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}

	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: user.clientemail,
		subject: 'Order Status',
		html: htmlTemplate,
	};


	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}


/* Order status update: canel */
Mail.sendOrderConfirmationMail = (data,user,title,sub_title,subject, result) => {


	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center>Hello '+user.clientname+'!</h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>'+title+'</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"><div><p>'+sub_title+'</div> </table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Billing Information<tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''
	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){
		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}

	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: user.clientemail,
		subject: subject,
		html: htmlTemplate,
	};

	sgMail.send(mailOptions).then(() => {
		console.log('Email sent:'+user.clientemail);
	}).catch((error) => {
		console.error(error);
	});

}

Mail.sendOrderedMail = (data, user, result) => {

	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center>Hello '+user.clientname+'!</h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>We’ve received your order.</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"><div><p>Your order is ready for pickup at location:'+data.location+' on '+ moment.utc(data.delivery_date).tz("PST8PDT").format("MMMM, Do YYYY") +'<p>You can also view your order in the mobile app by going to your profile and clicking on orders.</div></table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Billing Information<tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''
	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){
		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}

	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: user.clientemail,
		subject: 'Order Status',
		html: htmlTemplate,
	};


	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}


Mail.sendOrderedMailToOwner = (data, user, result) => {

	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center></h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>Order Received</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"></table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Customer Information<tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''

	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){

		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}


	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: "<EMAIL>",
		subject: 'New Order Received',
		html: htmlTemplate,
	};

	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});
}

/* Order status update: canel */
Mail.orderDeclineEmail = (data,user,title,sub_title,subject, result) => {


	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center>Hello '+user.clientname+'!</h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>'+title+'</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"><div><p>'+sub_title+'</div> </table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Billing Information<tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''
	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){
		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}

	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: user.clientemail,
		subject: subject,
		html: htmlTemplate,
	};

	sgMail.send(mailOptions).then(() => {
		console.log('Email sent:'+user.clientemail);
	}).catch((error) => {
		console.error(error);
	});

}

/* Order status update: ready for Pickup/ Completed */
Mail.sendOrderedUpdateMail = (data,user,title,sub_title,subject,pickuploc, result) => {


	var htmlTemplate = '<meta content="text/html; charset=UTF-8"http-equiv=Content-Type><title>Dr.Reed</title><meta content="width=device-width,initial-scale=1"name=viewport><link href="https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800&display=swap"rel=stylesheet><body style=margin:0;padding:0><div><link href=https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/css/font-awesome.css rel=stylesheet></div><table border=0 cellpadding=0 cellspacing=0 width=65% style=margin:auto><tr><td style="padding:10px 0 30px 0"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center style="border:1px solid #ccc;border-collapse:collapse"><tr><td style="padding:13px 0 15px 0;color:#153643;font-size:28px;font-weight:700;font-family:Montserrat,sans-serif;position:relative;background:#d5eac3;border-bottom:1px solid #ccc"width=100% align=center height=50><img alt=Logo src=http://**************/assets/img/brand/email_template_logo.png><tr><td style="padding:50px 50px 0 50px"bgcolor=#ffffff><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#153643;font-family:Montserrat,sans-serif;font-size:16px><h3 style=font-weight:700;font-size:18px;color:rgba(86,141,44,1);text-align:center>Hello '+user.clientname+'!</h3><h2 style=text-align:center;font-size:28px;margin:0;color:#000;font-weight:500>'+title+'</h2><tr><td style="padding:20px 20px 30px 20px;color:#000;font-family:Montserrat,sans-serif;font-size:16px;line-height:20px;text-align:center;font-weight:500"><div><p>'+sub_title+'</div> <div><p>'+pickuploc+'</div></table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3;width:65%">Order No. '+data.order_id+'<th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Billing Information<tbody><tr><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Placed on '+moment.utc(data.createdAt).tz("PST8PDT").format("MMMM, Do YYYY")+'<td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p>'

	let str = ''
	if(data.payment_details && data.payment_details.charges && data.payment_details.charges.data[0] && data.payment_details.charges.data[0].billing_details ){
		str = data.payment_details.charges.data[0].billing_details.name==null?'N/A':data.payment_details.charges.data[0].billing_details.name +'<br>'+data.payment_details.charges.data[0].billing_details.address.line1==null?'N/A':data.payment_details.charges.data[0].billing_details.address.line1+'<br>'+data.payment_details.charges.data[0].billing_details.address.city==null?'N/A':data.payment_details.charges.data[0].billing_details.address.city+', '+data.payment_details.charges.data[0].billing_details.address.state==null?'N/A':data.payment_details.charges.data[0].billing_details.address.state+', '+data.payment_details.charges.data[0].billing_details.address.postal_code==null?'N/A':data.payment_details.charges.data[0].billing_details.address.postal_code +'<br> United States.'
	}

	htmlTemplate +='<p>'+str+'</table><tr><td style="padding:0 50px 0 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><thead><tr><th style="text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3">Order Summary<th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><th style="text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;border-bottom:1px solid #b3b3b3"><tbody>'

	let txA = 0;
	for (var item of data.product) {
		txA +=item.tax_amount;

		htmlTemplate+='<tr><td style=text-align:left;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;width:60%><img alt=product src="'+item.product_id.poster_image+'" style=float:left;width:20%><div style="padding:20px 0 0 0"><h5 style="margin:0;font-size:16px;padding:0 0 5px 0">'+item.product_id.title+'</h5><p style=margin:0;color:#bfbfc9;font-size:14px>'+item.product_id.description.slice(0, 120)+'...</div><td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Qty: '+item.quantity+'<td style=text-align:center;font-size:16px;padding:20px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><p style=text-decoration:line-through>$'+(item.price).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<p style=color:rgba(86,141,44,1)>$'+(item.sub_total).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")
	}
	
	let tt = data.total_amount-txA;

	htmlTemplate+='<tfoot><tr><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">Sub Total<td style="text-align:center;font-size:16px;padding:20px 10px 10px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-top:1px solid #b3b3b3">$'+(tt).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500><td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>Tax<td style=text-align:center;font-size:16px;padding:10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500>$'+(txA).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'<tr><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>Total</b><td style="text-align:center;font-size:16px;padding:10px 10px 20px 10px;font-family:Montserrat,sans-serif;color:#000;font-weight:500;border-bottom:1px solid #b3b3b3"><b>$'+(data.total_amount).toFixed(2).replace(/\d(?=(\d{3})+\.)/g, "$&,")+'</b></table><tr><td style="padding:20px 50px 20px 50px"><table border=0 cellpadding=0 cellspacing=0 width=100% align=center><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>Need Assistance? Contact Us.<tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><p>We’ll do everything we can to make sure you have a great experience with us.<p>Email Us: <a href=mailto:<EMAIL> style=color:#0027ff><EMAIL></a><tr><td style="text-align:left;font-size:16px;padding:10px 20px 10px 0;font-family:Montserrat,sans-serif;color:#000;font-weight:500"><a href=https://www.reedanimalhospital.com/ style=color:#0027ff>https://www.reedanimalhospital.com/</a></table><tr><td style="background:#e8e8e8;padding:10px 30px 10px 30px"bgcolor=#8FAADC><table border=0 cellpadding=0 cellspacing=0 width=100%><tr><td style=color:#000;font-family:Montserrat,sans-serif;font-size:14px;opacity:.3 width=75%>© All Rights Reserved 2022<br><td width=25% align=right><table border=0 cellpadding=0 cellspacing=0></table></table></table></table>'


	let mailOptions = {
		from: process.env.MAILS,
		to: user.clientemail,
		subject: subject,
		html: htmlTemplate,
	};

	sgMail.send(mailOptions).then(() => {
		console.log('Email sent');
	}).catch((error) => {
		console.error(error);
	});

}



module.exports = Mail;

