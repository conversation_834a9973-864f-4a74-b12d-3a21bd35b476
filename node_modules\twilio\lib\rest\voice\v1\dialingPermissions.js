'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var BulkCountryUpdateList = require(
    './dialingPermissions/bulkCountryUpdate').BulkCountryUpdateList;
var CountryList = require('./dialingPermissions/country').CountryList;
var SettingsList = require('./dialingPermissions/settings').SettingsList;

var DialingPermissionsList;

/* jshint ignore:start */
/**
 * Initialize the DialingPermissionsList
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Voice.V1.DialingPermissionsList
 *
 * @param {Twilio.Voice.V1} version - Version of the resource
 */
/* jshint ignore:end */
DialingPermissionsList = function DialingPermissionsList(version) {
  /* jshint ignore:start */
  /**
   * @function dialingPermissions
   * @memberof Twilio.Voice.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Voice.V1.DialingPermissionsContext}
   */
  /* jshint ignore:end */
  function DialingPermissionsListInstance(sid) {
    return DialingPermissionsListInstance.get(sid);
  }

  DialingPermissionsListInstance._version = version;
  // Path Solution
  DialingPermissionsListInstance._solution = {};

  // Components
  DialingPermissionsListInstance._countries = undefined;
  DialingPermissionsListInstance._settings = undefined;
  DialingPermissionsListInstance._bulkCountryUpdates = undefined;

  Object.defineProperty(DialingPermissionsListInstance,
    'countries', {
      get: function countries() {
        if (!this._countries) {
          this._countries = new CountryList(this._version);
        }

        return this._countries;
      }
  });

  Object.defineProperty(DialingPermissionsListInstance,
    'settings', {
      get: function settings() {
        if (!this._settings) {
          this._settings = new SettingsList(this._version);
        }

        return this._settings;
      }
  });

  Object.defineProperty(DialingPermissionsListInstance,
    'bulkCountryUpdates', {
      get: function bulkCountryUpdates() {
        if (!this._bulkCountryUpdates) {
          this._bulkCountryUpdates = new BulkCountryUpdateList(this._version);
        }

        return this._bulkCountryUpdates;
      }
  });

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Voice.V1.DialingPermissionsList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DialingPermissionsListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DialingPermissionsListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return DialingPermissionsListInstance;
};

module.exports = {
  DialingPermissionsList: DialingPermissionsList
};
