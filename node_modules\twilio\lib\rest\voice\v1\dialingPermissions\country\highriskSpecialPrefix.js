'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var HighriskSpecialPrefixList;
var HighriskSpecialPrefixPage;
var HighriskSpecialPrefixInstance;

/* jshint ignore:start */
/**
 * Initialize the HighriskSpecialPrefixList
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList
 *
 * @param {Twilio.Voice.V1} version - Version of the resource
 * @param {string} isoCode - The ISO country code
 */
/* jshint ignore:end */
HighriskSpecialPrefixList = function HighriskSpecialPrefixList(version, isoCode)
    {
  /* jshint ignore:start */
  /**
   * @function highriskSpecialPrefixes
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixContext}
   */
  /* jshint ignore:end */
  function HighriskSpecialPrefixListInstance(sid) {
    return HighriskSpecialPrefixListInstance.get(sid);
  }

  HighriskSpecialPrefixListInstance._version = version;
  // Path Solution
  HighriskSpecialPrefixListInstance._solution = {isoCode: isoCode};
  HighriskSpecialPrefixListInstance._uri = `/DialingPermissions/Countries/${isoCode}/HighRiskSpecialPrefixes`;
  /* jshint ignore:start */
  /**
   * Streams HighriskSpecialPrefixInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  HighriskSpecialPrefixListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists HighriskSpecialPrefixInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  HighriskSpecialPrefixListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of HighriskSpecialPrefixInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  HighriskSpecialPrefixListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new HighriskSpecialPrefixPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of HighriskSpecialPrefixInstance records from the
   * API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  HighriskSpecialPrefixListInstance.getPage = function getPage(targetUrl,
      callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new HighriskSpecialPrefixPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  HighriskSpecialPrefixListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  HighriskSpecialPrefixListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return HighriskSpecialPrefixListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the HighriskSpecialPrefixPage
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {HighriskSpecialPrefixSolution} solution - Path solution
 *
 * @returns HighriskSpecialPrefixPage
 */
/* jshint ignore:end */
HighriskSpecialPrefixPage = function HighriskSpecialPrefixPage(version,
    response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(HighriskSpecialPrefixPage.prototype, Page.prototype);
HighriskSpecialPrefixPage.prototype.constructor = HighriskSpecialPrefixPage;

/* jshint ignore:start */
/**
 * Build an instance of HighriskSpecialPrefixInstance
 *
 * @function getInstance
 * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixPage#
 *
 * @param {HighriskSpecialPrefixPayload} payload - Payload response from the API
 *
 * @returns HighriskSpecialPrefixInstance
 */
/* jshint ignore:end */
HighriskSpecialPrefixPage.prototype.getInstance = function getInstance(payload)
    {
  return new HighriskSpecialPrefixInstance(this._version, payload, this._solution.isoCode);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
HighriskSpecialPrefixPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

HighriskSpecialPrefixPage.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the HighriskSpecialPrefixContext
 *
 * PLEASE NOTE that this class contains preview products that are subject to
 * change. Use them with caution. If you currently do not have developer preview
 * access, <NAME_EMAIL>.
 *
 * @constructor Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixInstance
 *
 * @property {string} prefix -
 *          A prefix that includes the E.164 assigned country code
 *
 * @param {V1} version - Version of the resource
 * @param {HighriskSpecialPrefixPayload} payload - The instance payload
 * @param {iso_country_code} isoCode - The ISO country code
 */
/* jshint ignore:end */
HighriskSpecialPrefixInstance = function HighriskSpecialPrefixInstance(version,
    payload, isoCode) {
  this._version = version;

  // Marshaled Properties
  this.prefix = payload.prefix; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {isoCode: isoCode, };
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.DialingPermissionsContext.CountryContext.HighriskSpecialPrefixInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
HighriskSpecialPrefixInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

HighriskSpecialPrefixInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  HighriskSpecialPrefixList: HighriskSpecialPrefixList,
  HighriskSpecialPrefixPage: HighriskSpecialPrefixPage,
  HighriskSpecialPrefixInstance: HighriskSpecialPrefixInstance
};
