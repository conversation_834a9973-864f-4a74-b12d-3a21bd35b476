'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var FormList;
var FormPage;
var FormInstance;
var FormContext;

/* jshint ignore:start */
/**
 * Initialize the FormList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Verify.V2.FormList
 *
 * @param {Twilio.Verify.V2} version - Version of the resource
 */
/* jshint ignore:end */
FormList = function FormList(version) {
  /* jshint ignore:start */
  /**
   * @function forms
   * @memberof Twilio.Verify.V2#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Verify.V2.FormContext}
   */
  /* jshint ignore:end */
  function FormListInstance(sid) {
    return FormListInstance.get(sid);
  }

  FormListInstance._version = version;
  // Path Solution
  FormListInstance._solution = {};
  /* jshint ignore:start */
  /**
   * Constructs a form
   *
   * @function get
   * @memberof Twilio.Verify.V2.FormList#
   *
   * @param {string} formType - The Type of this Form
   *
   * @returns {Twilio.Verify.V2.FormContext}
   */
  /* jshint ignore:end */
  FormListInstance.get = function get(formType) {
    return new FormContext(this._version, formType);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Verify.V2.FormList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FormListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FormListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return FormListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FormPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Verify.V2.FormPage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FormSolution} solution - Path solution
 *
 * @returns FormPage
 */
/* jshint ignore:end */
FormPage = function FormPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FormPage.prototype, Page.prototype);
FormPage.prototype.constructor = FormPage;

/* jshint ignore:start */
/**
 * Build an instance of FormInstance
 *
 * @function getInstance
 * @memberof Twilio.Verify.V2.FormPage#
 *
 * @param {FormPayload} payload - Payload response from the API
 *
 * @returns FormInstance
 */
/* jshint ignore:end */
FormPage.prototype.getInstance = function getInstance(payload) {
  return new FormInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.FormPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FormPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FormPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FormContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Verify.V2.FormInstance
 *
 * @property {form.form_types} formType - The Type of this Form
 * @property {object} forms -
 *          Object that contains the available forms for this type.
 * @property {object} formMeta -
 *          Additional information for the available forms for this type.
 * @property {string} url - The URL to access the forms for this type.
 *
 * @param {V2} version - Version of the resource
 * @param {FormPayload} payload - The instance payload
 * @param {form:enum:form_types} formType - The Type of this Form
 */
/* jshint ignore:end */
FormInstance = function FormInstance(version, payload, formType) {
  this._version = version;

  // Marshaled Properties
  this.formType = payload.form_type; // jshint ignore:line
  this.forms = payload.forms; // jshint ignore:line
  this.formMeta = payload.form_meta; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {formType: formType || this.formType, };
};

Object.defineProperty(FormInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new FormContext(this._version, this._solution.formType);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a FormInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.FormInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FormInstance
 */
/* jshint ignore:end */
FormInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.FormInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FormInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FormInstance.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FormContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Verify.V2.FormContext
 *
 * @param {V2} version - Version of the resource
 * @param {form:enum:form_types} formType - The Type of this Form
 */
/* jshint ignore:end */
FormContext = function FormContext(version, formType) {
  this._version = version;

  // Path Solution
  this._solution = {formType: formType, };
  this._uri = `/Forms/${formType}`;
};

/* jshint ignore:start */
/**
 * fetch a FormInstance
 *
 * @function fetch
 * @memberof Twilio.Verify.V2.FormContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FormInstance
 */
/* jshint ignore:end */
FormContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new FormInstance(this._version, payload, this._solution.formType));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Verify.V2.FormContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
FormContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

FormContext.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FormList: FormList,
  FormPage: FormPage,
  FormInstance: FormInstance,
  FormContext: FormContext
};
