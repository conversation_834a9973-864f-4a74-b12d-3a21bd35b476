'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var FleetList;
var FleetPage;
var FleetInstance;
var FleetContext;

/* jshint ignore:start */
/**
 * Initialize the FleetList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.FleetList
 *
 * @param {Twilio.Supersim.V1} version - Version of the resource
 */
/* jshint ignore:end */
FleetList = function FleetList(version) {
  /* jshint ignore:start */
  /**
   * @function fleets
   * @memberof Twilio.Supersim.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Supersim.V1.FleetContext}
   */
  /* jshint ignore:end */
  function FleetListInstance(sid) {
    return FleetListInstance.get(sid);
  }

  FleetListInstance._version = version;
  // Path Solution
  FleetListInstance._solution = {};
  FleetListInstance._uri = `/Fleets`;
  /* jshint ignore:start */
  /**
   * create a FleetInstance
   *
   * @function create
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.networkAccessProfile -
   *          The SID or unique name of the Network Access Profile of the Fleet
   * @param {string} [opts.uniqueName] -
   *          An application-defined string that uniquely identifies the resource
   * @param {boolean} [opts.dataEnabled] -
   *          Defines whether SIMs in the Fleet are capable of using data connectivity
   * @param {number} [opts.dataLimit] -
   *          The total data usage (download and upload combined) in Megabytes that each Super SIM resource assigned to the Fleet can consume
   * @param {string} [opts.ipCommandsUrl] -
   *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an IP Command from your device
   * @param {string} [opts.ipCommandsMethod] -
   *          A string representing the HTTP method to use when making a request to `ip_commands_url`
   * @param {boolean} [opts.smsCommandsEnabled] -
   *          Defines whether SIMs in the Fleet are capable of sending and receiving machine-to-machine SMS via Commands
   * @param {string} [opts.smsCommandsUrl] -
   *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an SMS from your device to the SMS Commands number
   * @param {string} [opts.smsCommandsMethod] -
   *          A string representing the HTTP method to use when making a request to `sms_commands_url`
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed FleetInstance
   */
  /* jshint ignore:end */
  FleetListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['networkAccessProfile'])) {
      throw new Error('Required parameter "opts[\'networkAccessProfile\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'NetworkAccessProfile': _.get(opts, 'networkAccessProfile'),
      'UniqueName': _.get(opts, 'uniqueName'),
      'DataEnabled': serialize.bool(_.get(opts, 'dataEnabled')),
      'DataLimit': _.get(opts, 'dataLimit'),
      'IpCommandsUrl': _.get(opts, 'ipCommandsUrl'),
      'IpCommandsMethod': _.get(opts, 'ipCommandsMethod'),
      'SmsCommandsEnabled': serialize.bool(_.get(opts, 'smsCommandsEnabled')),
      'SmsCommandsUrl': _.get(opts, 'smsCommandsUrl'),
      'SmsCommandsMethod': _.get(opts, 'smsCommandsMethod')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FleetInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams FleetInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.networkAccessProfile] -
   *          The SID or unique name of the Network Access Profile of the Fleet
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  FleetListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists FleetInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.networkAccessProfile] -
   *          The SID or unique name of the Network Access Profile of the Fleet
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FleetListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of FleetInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.networkAccessProfile] -
   *          The SID or unique name of the Network Access Profile of the Fleet
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FleetListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'NetworkAccessProfile': _.get(opts, 'networkAccessProfile'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FleetPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of FleetInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FleetListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new FleetPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a fleet
   *
   * @function get
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @param {string} sid - The SID that identifies the resource to fetch
   *
   * @returns {Twilio.Supersim.V1.FleetContext}
   */
  /* jshint ignore:end */
  FleetListInstance.get = function get(sid) {
    return new FleetContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Supersim.V1.FleetList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FleetListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FleetListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return FleetListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FleetPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.FleetPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FleetSolution} solution - Path solution
 *
 * @returns FleetPage
 */
/* jshint ignore:end */
FleetPage = function FleetPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FleetPage.prototype, Page.prototype);
FleetPage.prototype.constructor = FleetPage;

/* jshint ignore:start */
/**
 * Build an instance of FleetInstance
 *
 * @function getInstance
 * @memberof Twilio.Supersim.V1.FleetPage#
 *
 * @param {FleetPayload} payload - Payload response from the API
 *
 * @returns FleetInstance
 */
/* jshint ignore:end */
FleetPage.prototype.getInstance = function getInstance(payload) {
  return new FleetInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.FleetPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FleetPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FleetPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FleetContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.FleetInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} uniqueName -
 *          An application-defined string that uniquely identifies the resource
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the Fleet resource
 * @property {boolean} dataEnabled -
 *          Defines whether SIMs in the Fleet are capable of using data connectivity
 * @property {number} dataLimit -
 *          The total data usage (download and upload combined) in Megabytes that each Super SIM assigned to the Fleet can consume
 * @property {fleet.data_metering} dataMetering -
 *          The model by which a SIM is metered and billed
 * @property {boolean} smsCommandsEnabled -
 *          Defines whether SIMs in the Fleet are capable of sending and receiving machine-to-machine SMS via Commands
 * @property {string} smsCommandsUrl -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an SMS from your device to the SMS Commands number
 * @property {string} smsCommandsMethod -
 *          A string representing the HTTP method to use when making a request to `sms_commands_url`
 * @property {string} networkAccessProfileSid -
 *          The SID of the Network Access Profile of the Fleet
 * @property {string} ipCommandsUrl -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an IP Command from your device
 * @property {string} ipCommandsMethod -
 *          A string representing the HTTP method to use when making a request to `ip_commands_url`
 *
 * @param {V1} version - Version of the resource
 * @param {FleetPayload} payload - The instance payload
 * @param {sid_like} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
FleetInstance = function FleetInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.uniqueName = payload.unique_name; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.dataEnabled = payload.data_enabled; // jshint ignore:line
  this.dataLimit = deserialize.integer(payload.data_limit); // jshint ignore:line
  this.dataMetering = payload.data_metering; // jshint ignore:line
  this.smsCommandsEnabled = payload.sms_commands_enabled; // jshint ignore:line
  this.smsCommandsUrl = payload.sms_commands_url; // jshint ignore:line
  this.smsCommandsMethod = payload.sms_commands_method; // jshint ignore:line
  this.networkAccessProfileSid = payload.network_access_profile_sid; // jshint ignore:line
  this.ipCommandsUrl = payload.ip_commands_url; // jshint ignore:line
  this.ipCommandsMethod = payload.ip_commands_method; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(FleetInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new FleetContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a FleetInstance
 *
 * @function fetch
 * @memberof Twilio.Supersim.V1.FleetInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FleetInstance
 */
/* jshint ignore:end */
FleetInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a FleetInstance
 *
 * @function update
 * @memberof Twilio.Supersim.V1.FleetInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.uniqueName] -
 *          An application-defined string that uniquely identifies the resource
 * @param {string} [opts.networkAccessProfile] -
 *          The SID or unique name of the Network Access Profile of the Fleet
 * @param {string} [opts.ipCommandsUrl] -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an IP Command from your device
 * @param {string} [opts.ipCommandsMethod] -
 *          A string representing the HTTP method to use when making a request to `ip_commands_url`
 * @param {string} [opts.smsCommandsUrl] -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an SMS from your device to the SMS Commands number
 * @param {string} [opts.smsCommandsMethod] -
 *          A string representing the HTTP method to use when making a request to `sms_commands_url`
 * @param {number} [opts.dataLimit] -
 *          The total data usage (download and upload combined) in Megabytes that each Super SIM assigned to the Fleet can consume
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FleetInstance
 */
/* jshint ignore:end */
FleetInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.FleetInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FleetInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FleetInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FleetContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.FleetContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
FleetContext = function FleetContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/Fleets/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a FleetInstance
 *
 * @function fetch
 * @memberof Twilio.Supersim.V1.FleetContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FleetInstance
 */
/* jshint ignore:end */
FleetContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new FleetInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a FleetInstance
 *
 * @function update
 * @memberof Twilio.Supersim.V1.FleetContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.uniqueName] -
 *          An application-defined string that uniquely identifies the resource
 * @param {string} [opts.networkAccessProfile] -
 *          The SID or unique name of the Network Access Profile of the Fleet
 * @param {string} [opts.ipCommandsUrl] -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an IP Command from your device
 * @param {string} [opts.ipCommandsMethod] -
 *          A string representing the HTTP method to use when making a request to `ip_commands_url`
 * @param {string} [opts.smsCommandsUrl] -
 *          The URL that will receive a webhook when a Super SIM in the Fleet is used to send an SMS from your device to the SMS Commands number
 * @param {string} [opts.smsCommandsMethod] -
 *          A string representing the HTTP method to use when making a request to `sms_commands_url`
 * @param {number} [opts.dataLimit] -
 *          The total data usage (download and upload combined) in Megabytes that each Super SIM assigned to the Fleet can consume
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FleetInstance
 */
/* jshint ignore:end */
FleetContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'UniqueName': _.get(opts, 'uniqueName'),
    'NetworkAccessProfile': _.get(opts, 'networkAccessProfile'),
    'IpCommandsUrl': _.get(opts, 'ipCommandsUrl'),
    'IpCommandsMethod': _.get(opts, 'ipCommandsMethod'),
    'SmsCommandsUrl': _.get(opts, 'smsCommandsUrl'),
    'SmsCommandsMethod': _.get(opts, 'smsCommandsMethod'),
    'DataLimit': _.get(opts, 'dataLimit')
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new FleetInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.FleetContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
FleetContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

FleetContext.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FleetList: FleetList,
  FleetPage: FleetPage,
  FleetInstance: FleetInstance,
  FleetContext: FleetContext
};
