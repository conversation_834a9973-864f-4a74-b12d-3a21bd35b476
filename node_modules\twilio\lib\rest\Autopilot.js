'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./autopilot/V1');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize autopilot domain
 *
 * @constructor Twilio.Autopilot
 *
 * @property {Twilio.Autopilot.V1} v1 - v1 version
 * @property {Twilio.Autopilot.V1.AssistantList} assistants - assistants resource
 * @property {Twilio.Autopilot.V1.RestoreAssistantList} restoreAssistant -
 *          restoreAssistant resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Autopilot(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://autopilot.twilio.com');

  // Versions
  this._v1 = undefined;
}

_.extend(Autopilot.prototype, Domain.prototype);
Autopilot.prototype.constructor = Autopilot;

Object.defineProperty(Autopilot.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Autopilot.prototype,
  'assistants', {
    get: function() {
      return this.v1.assistants;
    }
});

Object.defineProperty(Autopilot.prototype,
  'restoreAssistant', {
    get: function() {
      return this.v1.restoreAssistant;
    }
});

module.exports = Autopilot;
