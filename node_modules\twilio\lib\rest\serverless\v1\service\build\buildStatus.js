'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var BuildStatusList;
var BuildStatusPage;
var BuildStatusInstance;
var BuildStatusContext;

/* jshint ignore:start */
/**
 * Initialize the BuildStatusList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusList
 *
 * @param {Twilio.Serverless.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 * @param {string} sid - The unique string that identifies the Build resource
 */
/* jshint ignore:end */
BuildStatusList = function BuildStatusList(version, serviceSid, sid) {
  /* jshint ignore:start */
  /**
   * @function buildStatus
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusContext}
   */
  /* jshint ignore:end */
  function BuildStatusListInstance(sid) {
    return BuildStatusListInstance.get(sid);
  }

  BuildStatusListInstance._version = version;
  // Path Solution
  BuildStatusListInstance._solution = {serviceSid: serviceSid, sid: sid};
  /* jshint ignore:start */
  /**
   * Constructs a build_status
   *
   * @function get
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusList#
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusContext}
   */
  /* jshint ignore:end */
  BuildStatusListInstance.get = function get() {
    return new BuildStatusContext(this._version, this._solution.serviceSid, this._solution.sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BuildStatusListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BuildStatusListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return BuildStatusListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BuildStatusPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BuildStatusSolution} solution - Path solution
 *
 * @returns BuildStatusPage
 */
/* jshint ignore:end */
BuildStatusPage = function BuildStatusPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BuildStatusPage.prototype, Page.prototype);
BuildStatusPage.prototype.constructor = BuildStatusPage;

/* jshint ignore:start */
/**
 * Build an instance of BuildStatusInstance
 *
 * @function getInstance
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusPage#
 *
 * @param {BuildStatusPayload} payload - Payload response from the API
 *
 * @returns BuildStatusInstance
 */
/* jshint ignore:end */
BuildStatusPage.prototype.getInstance = function getInstance(payload) {
  return new BuildStatusInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.sid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildStatusPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BuildStatusPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BuildStatusContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusInstance
 *
 * @property {string} sid - The unique string that identifies the Build resource
 * @property {string} accountSid -
 *          The SID of the Account that created the Build resource
 * @property {string} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 * @property {build_status.status} status - The status of the Build
 * @property {string} url - The absolute URL of the Build Status resource
 *
 * @param {V1} version - Version of the resource
 * @param {BuildStatusPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 * @param {sid} sid - The unique string that identifies the Build resource
 */
/* jshint ignore:end */
BuildStatusInstance = function BuildStatusInstance(version, payload, serviceSid,
                                                    sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sid: sid, };
};

Object.defineProperty(BuildStatusInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BuildStatusContext(
          this._version,
          this._solution.serviceSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a BuildStatusInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildStatusInstance
 */
/* jshint ignore:end */
BuildStatusInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildStatusInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BuildStatusInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BuildStatusContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Service to fetch the Build resource from
 * @param {sid} sid - The SID of the Build resource to fetch
 */
/* jshint ignore:end */
BuildStatusContext = function BuildStatusContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Builds/${sid}/Status`;
};

/* jshint ignore:start */
/**
 * fetch a BuildStatusInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildStatusInstance
 */
/* jshint ignore:end */
BuildStatusContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BuildStatusInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildStatusContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BuildStatusContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BuildStatusList: BuildStatusList,
  BuildStatusPage: BuildStatusPage,
  BuildStatusInstance: BuildStatusInstance,
  BuildStatusContext: BuildStatusContext
};
