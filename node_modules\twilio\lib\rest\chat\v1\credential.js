'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var CredentialList;
var CredentialPage;
var CredentialInstance;
var CredentialContext;

/* jshint ignore:start */
/**
 * Initialize the CredentialList
 *
 * @constructor Twilio.Chat.V1.CredentialList
 *
 * @param {Twilio.Chat.V1} version - Version of the resource
 */
/* jshint ignore:end */
CredentialList = function CredentialList(version) {
  /* jshint ignore:start */
  /**
   * @function credentials
   * @memberof Twilio.Chat.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Chat.V1.CredentialContext}
   */
  /* jshint ignore:end */
  function CredentialListInstance(sid) {
    return CredentialListInstance.get(sid);
  }

  CredentialListInstance._version = version;
  // Path Solution
  CredentialListInstance._solution = {};
  CredentialListInstance._uri = `/Credentials`;
  /* jshint ignore:start */
  /**
   * Streams CredentialInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  CredentialListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists CredentialInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CredentialListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of CredentialInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CredentialListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new CredentialPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of CredentialInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CredentialListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new CredentialPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * create a CredentialInstance
   *
   * @function create
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {object} opts - Options for request
   * @param {credential.push_service} opts.type -
   *          The type of push-notification service the credential is for
   * @param {string} [opts.friendlyName] - A string to describe the resource
   * @param {string} [opts.certificate] -
   *          [APN only] The URL encoded representation of the certificate
   * @param {string} [opts.privateKey] -
   *          [APN only] The URL encoded representation of the private key
   * @param {boolean} [opts.sandbox] -
   *          [APN only] Whether to send the credential to sandbox APNs
   * @param {string} [opts.apiKey] -
   *          [GCM only] The API key for the project that was obtained from the Google Developer console for your GCM Service application credential
   * @param {string} [opts.secret] -
   *          [FCM only] The Server key of your project from Firebase console
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed CredentialInstance
   */
  /* jshint ignore:end */
  CredentialListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['type'])) {
      throw new Error('Required parameter "opts[\'type\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'Type': _.get(opts, 'type'),
      'FriendlyName': _.get(opts, 'friendlyName'),
      'Certificate': _.get(opts, 'certificate'),
      'PrivateKey': _.get(opts, 'privateKey'),
      'Sandbox': serialize.bool(_.get(opts, 'sandbox')),
      'ApiKey': _.get(opts, 'apiKey'),
      'Secret': _.get(opts, 'secret')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new CredentialInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a credential
   *
   * @function get
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Chat.V1.CredentialContext}
   */
  /* jshint ignore:end */
  CredentialListInstance.get = function get(sid) {
    return new CredentialContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Chat.V1.CredentialList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  CredentialListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  CredentialListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return CredentialListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the CredentialPage
 *
 * @constructor Twilio.Chat.V1.CredentialPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {CredentialSolution} solution - Path solution
 *
 * @returns CredentialPage
 */
/* jshint ignore:end */
CredentialPage = function CredentialPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(CredentialPage.prototype, Page.prototype);
CredentialPage.prototype.constructor = CredentialPage;

/* jshint ignore:start */
/**
 * Build an instance of CredentialInstance
 *
 * @function getInstance
 * @memberof Twilio.Chat.V1.CredentialPage#
 *
 * @param {CredentialPayload} payload - Payload response from the API
 *
 * @returns CredentialInstance
 */
/* jshint ignore:end */
CredentialPage.prototype.getInstance = function getInstance(payload) {
  return new CredentialInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V1.CredentialPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
CredentialPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

CredentialPage.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the CredentialContext
 *
 * @constructor Twilio.Chat.V1.CredentialInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {credential.push_service} type -
 *          The type of push-notification service the credential is for
 * @property {string} sandbox -
 *          [APN only] Whether to send the credential to sandbox APNs
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the Credential resource
 *
 * @param {V1} version - Version of the resource
 * @param {CredentialPayload} payload - The instance payload
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
CredentialInstance = function CredentialInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.type = payload.type; // jshint ignore:line
  this.sandbox = payload.sandbox; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(CredentialInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new CredentialContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a CredentialInstance
 *
 * @function fetch
 * @memberof Twilio.Chat.V1.CredentialInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a CredentialInstance
 *
 * @function update
 * @memberof Twilio.Chat.V1.CredentialInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.certificate] -
 *          [APN only] The URL encoded representation of the certificate
 * @param {string} [opts.privateKey] -
 *          [APN only] The URL encoded representation of the private key
 * @param {boolean} [opts.sandbox] -
 *          [APN only] Whether to send the credential to sandbox APNs
 * @param {string} [opts.apiKey] -
 *          [GCM only] The API key for the project that was obtained from the Google Developer console for your GCM Service application credential
 * @param {string} [opts.secret] -
 *          [FCM only] The Server key of your project from Firebase console
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * remove a CredentialInstance
 *
 * @function remove
 * @memberof Twilio.Chat.V1.CredentialInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V1.CredentialInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
CredentialInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

CredentialInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the CredentialContext
 *
 * @constructor Twilio.Chat.V1.CredentialContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
CredentialContext = function CredentialContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/Credentials/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a CredentialInstance
 *
 * @function fetch
 * @memberof Twilio.Chat.V1.CredentialContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new CredentialInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a CredentialInstance
 *
 * @function update
 * @memberof Twilio.Chat.V1.CredentialContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.certificate] -
 *          [APN only] The URL encoded representation of the certificate
 * @param {string} [opts.privateKey] -
 *          [APN only] The URL encoded representation of the private key
 * @param {boolean} [opts.sandbox] -
 *          [APN only] Whether to send the credential to sandbox APNs
 * @param {string} [opts.apiKey] -
 *          [GCM only] The API key for the project that was obtained from the Google Developer console for your GCM Service application credential
 * @param {string} [opts.secret] -
 *          [FCM only] The Server key of your project from Firebase console
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'FriendlyName': _.get(opts, 'friendlyName'),
    'Certificate': _.get(opts, 'certificate'),
    'PrivateKey': _.get(opts, 'privateKey'),
    'Sandbox': serialize.bool(_.get(opts, 'sandbox')),
    'ApiKey': _.get(opts, 'apiKey'),
    'Secret': _.get(opts, 'secret')
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new CredentialInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a CredentialInstance
 *
 * @function remove
 * @memberof Twilio.Chat.V1.CredentialContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CredentialInstance
 */
/* jshint ignore:end */
CredentialContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V1.CredentialContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
CredentialContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

CredentialContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  CredentialList: CredentialList,
  CredentialPage: CredentialPage,
  CredentialInstance: CredentialInstance,
  CredentialContext: CredentialContext
};
