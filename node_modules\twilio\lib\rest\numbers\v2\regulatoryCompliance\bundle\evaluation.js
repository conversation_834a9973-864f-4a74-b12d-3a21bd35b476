'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var EvaluationList;
var EvaluationPage;
var EvaluationInstance;
var EvaluationContext;

/* jshint ignore:start */
/**
 * Initialize the EvaluationList
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList
 *
 * @param {Twilio.Numbers.V2} version - Version of the resource
 * @param {string} bundleSid - The unique string that identifies the resource
 */
/* jshint ignore:end */
EvaluationList = function EvaluationList(version, bundleSid) {
  /* jshint ignore:start */
  /**
   * @function evaluations
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationContext}
   */
  /* jshint ignore:end */
  function EvaluationListInstance(sid) {
    return EvaluationListInstance.get(sid);
  }

  EvaluationListInstance._version = version;
  // Path Solution
  EvaluationListInstance._solution = {bundleSid: bundleSid};
  EvaluationListInstance._uri = `/RegulatoryCompliance/Bundles/${bundleSid}/Evaluations`;
  /* jshint ignore:start */
  /**
   * create a EvaluationInstance
   *
   * @function create
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed EvaluationInstance
   */
  /* jshint ignore:end */
  EvaluationListInstance.create = function create(callback) {
    var deferred = Q.defer();
    var promise = this._version.create({uri: this._uri, method: 'POST'});

    promise = promise.then(function(payload) {
      deferred.resolve(new EvaluationInstance(
        this._version,
        payload,
        this._solution.bundleSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams EvaluationInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  EvaluationListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists EvaluationInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EvaluationListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of EvaluationInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EvaluationListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EvaluationPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of EvaluationInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EvaluationListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new EvaluationPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a evaluation
   *
   * @function get
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @param {string} sid - The unique string that identifies the Evaluation resource
   *
   * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationContext}
   */
  /* jshint ignore:end */
  EvaluationListInstance.get = function get(sid) {
    return new EvaluationContext(this._version, this._solution.bundleSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  EvaluationListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  EvaluationListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return EvaluationListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the EvaluationPage
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationPage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {EvaluationSolution} solution - Path solution
 *
 * @returns EvaluationPage
 */
/* jshint ignore:end */
EvaluationPage = function EvaluationPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(EvaluationPage.prototype, Page.prototype);
EvaluationPage.prototype.constructor = EvaluationPage;

/* jshint ignore:start */
/**
 * Build an instance of EvaluationInstance
 *
 * @function getInstance
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationPage#
 *
 * @param {EvaluationPayload} payload - Payload response from the API
 *
 * @returns EvaluationInstance
 */
/* jshint ignore:end */
EvaluationPage.prototype.getInstance = function getInstance(payload) {
  return new EvaluationInstance(this._version, payload, this._solution.bundleSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
EvaluationPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EvaluationPage.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EvaluationContext
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationInstance
 *
 * @property {string} sid -
 *          The unique string that identifies the Evaluation resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} regulationSid - The unique string of a regulation
 * @property {string} bundleSid - The unique string that identifies the resource
 * @property {evaluation.status} status -
 *          The compliance status of the Evaluation resource
 * @property {object} results - The results of the Evaluation resource
 * @property {Date} dateCreated - The date_created
 * @property {string} url - The url
 *
 * @param {V2} version - Version of the resource
 * @param {EvaluationPayload} payload - The instance payload
 * @param {sid} bundleSid - The unique string that identifies the resource
 * @param {sid} sid - The unique string that identifies the Evaluation resource
 */
/* jshint ignore:end */
EvaluationInstance = function EvaluationInstance(version, payload, bundleSid,
                                                  sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.regulationSid = payload.regulation_sid; // jshint ignore:line
  this.bundleSid = payload.bundle_sid; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.results = payload.results; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {bundleSid: bundleSid, sid: sid || this.sid, };
};

Object.defineProperty(EvaluationInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new EvaluationContext(this._version, this._solution.bundleSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a EvaluationInstance
 *
 * @function fetch
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EvaluationInstance
 */
/* jshint ignore:end */
EvaluationInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
EvaluationInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EvaluationInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EvaluationContext
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationContext
 *
 * @param {V2} version - Version of the resource
 * @param {sid} bundleSid - The unique string that identifies the resource
 * @param {sid} sid - The unique string that identifies the Evaluation resource
 */
/* jshint ignore:end */
EvaluationContext = function EvaluationContext(version, bundleSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {bundleSid: bundleSid, sid: sid, };
  this._uri = `/RegulatoryCompliance/Bundles/${bundleSid}/Evaluations/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a EvaluationInstance
 *
 * @function fetch
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EvaluationInstance
 */
/* jshint ignore:end */
EvaluationContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new EvaluationInstance(
      this._version,
      payload,
      this._solution.bundleSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
EvaluationContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

EvaluationContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  EvaluationList: EvaluationList,
  EvaluationPage: EvaluationPage,
  EvaluationInstance: EvaluationInstance,
  EvaluationContext: EvaluationContext
};
