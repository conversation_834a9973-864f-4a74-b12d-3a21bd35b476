'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./conversations/V1');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize conversations domain
 *
 * @constructor Twilio.Conversations
 *
 * @property {Twilio.Conversations.V1} v1 - v1 version
 * @property {Twilio.Conversations.V1.ConfigurationList} configuration -
 *          configuration resource
 * @property {Twilio.Conversations.V1.AddressConfigurationList} addressConfigurations -
 *          addressConfigurations resource
 * @property {Twilio.Conversations.V1.ConversationList} conversations -
 *          conversations resource
 * @property {Twilio.Conversations.V1.CredentialList} credentials -
 *          credentials resource
 * @property {Twilio.Conversations.V1.ParticipantConversationList} participantConversations -
 *          participantConversations resource
 * @property {Twilio.Conversations.V1.RoleList} roles - roles resource
 * @property {Twilio.Conversations.V1.ServiceList} services - services resource
 * @property {Twilio.Conversations.V1.UserList} users - users resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Conversations(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://conversations.twilio.com');

  // Versions
  this._v1 = undefined;
}

_.extend(Conversations.prototype, Domain.prototype);
Conversations.prototype.constructor = Conversations;

Object.defineProperty(Conversations.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Conversations.prototype,
  'configuration', {
    get: function() {
      return this.v1.configuration;
    }
});

Object.defineProperty(Conversations.prototype,
  'addressConfigurations', {
    get: function() {
      return this.v1.addressConfigurations;
    }
});

Object.defineProperty(Conversations.prototype,
  'conversations', {
    get: function() {
      return this.v1.conversations;
    }
});

Object.defineProperty(Conversations.prototype,
  'credentials', {
    get: function() {
      return this.v1.credentials;
    }
});

Object.defineProperty(Conversations.prototype,
  'participantConversations', {
    get: function() {
      return this.v1.participantConversations;
    }
});

Object.defineProperty(Conversations.prototype,
  'roles', {
    get: function() {
      return this.v1.roles;
    }
});

Object.defineProperty(Conversations.prototype,
  'services', {
    get: function() {
      return this.v1.services;
    }
});

Object.defineProperty(Conversations.prototype,
  'users', {
    get: function() {
      return this.v1.users;
    }
});

module.exports = Conversations;
