'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./ipMessaging/V1');  /* jshint ignore:line */
var V2 = require('./ipMessaging/V2');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize ip_messaging domain
 *
 * @constructor Twilio.IpMessaging
 *
 * @property {Twilio.IpMessaging.V1} v1 - v1 version
 * @property {Twilio.IpMessaging.V2} v2 - v2 version
 * @property {Twilio.IpMessaging.V2.CredentialList} credentials -
 *          credentials resource
 * @property {Twilio.IpMessaging.V2.ServiceList} services - services resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function IpMessaging(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://ip-messaging.twilio.com');

  // Versions
  this._v1 = undefined;
  this._v2 = undefined;
}

_.extend(IpMessaging.prototype, Domain.prototype);
IpMessaging.prototype.constructor = IpMessaging;

Object.defineProperty(IpMessaging.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(IpMessaging.prototype,
  'v2', {
    get: function() {
      this._v2 = this._v2 || new V2(this);
      return this._v2;
    }
});

Object.defineProperty(IpMessaging.prototype,
  'credentials', {
    get: function() {
      return this.v2.credentials;
    }
});

Object.defineProperty(IpMessaging.prototype,
  'services', {
    get: function() {
      return this.v2.services;
    }
});

module.exports = IpMessaging;
