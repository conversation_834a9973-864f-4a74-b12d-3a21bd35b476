{"name": "doctor-reed", "version": "1.0.0", "description": "", "main": "server.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node server.js"}, "author": "", "license": "ISC", "dependencies": {"@sendgrid/mail": "^7.4.6", "async": "^3.2.1", "await": "^0.2.6", "bcrypt": "^5.0.1", "body-parser": "^1.19.0", "cors": "^2.8.5", "crypto": "^1.0.1", "dateformat": "^4.5.1", "debug": "^4.3.2", "dotenv": "^8.6.0", "express": "^4.17.1", "express-fileupload": "^1.2.1", "fcm-node": "^1.6.1", "firebase-admin": "^12.1.0", "html-pdf-node": "^1.0.8", "jade": "^0.31.2", "jsonwebtoken": "^8.5.1", "lodash": "^4.17.21", "mailgun-js": "^0.6.7", "moment": "^2.29.1", "moment-timezone": "^0.5.34", "mongoose": "^5.13.9", "morgan": "^1.10.0", "multer": "^1.4.3", "node-fetch": "^2.6.2", "nodejs-base64": "^2.0.0", "nodemailer": "^6.6.3", "nodemon": "^3.1.10", "opentok": "^2.3.2", "path": "^0.12.7", "qrcode": "^1.4.4", "serve-index": "^1.9.1", "stripe": "^8.174.0", "twilio": "^3.71.2"}}