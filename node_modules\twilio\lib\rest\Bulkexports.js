'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./bulkexports/V1');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize bulkexports domain
 *
 * @constructor Twilio.Bulkexports
 *
 * @property {Twilio.Bulkexports.V1} v1 - v1 version
 * @property {Twilio.Bulkexports.V1.ExportList} exports - exports resource
 * @property {Twilio.Bulkexports.V1.ExportConfigurationList} exportConfiguration -
 *          exportConfiguration resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Bulkexports(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://bulkexports.twilio.com');

  // Versions
  this._v1 = undefined;
}

_.extend(Bulkexports.prototype, Domain.prototype);
Bulkexports.prototype.constructor = Bulkexports;

Object.defineProperty(Bulkexports.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Bulkexports.prototype,
  'exports', {
    get: function() {
      return this.v1.exports;
    }
});

Object.defineProperty(Bulkexports.prototype,
  'exportConfiguration', {
    get: function() {
      return this.v1.exportConfiguration;
    }
});

module.exports = Bulkexports;
