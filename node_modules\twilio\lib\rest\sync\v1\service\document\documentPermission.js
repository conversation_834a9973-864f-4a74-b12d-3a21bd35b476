'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var serialize = require(
    '../../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var DocumentPermissionList;
var DocumentPermissionPage;
var DocumentPermissionInstance;
var DocumentPermissionContext;

/* jshint ignore:start */
/**
 * Initialize the DocumentPermissionList
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList
 *
 * @param {Twilio.Sync.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 * @param {string} documentSid - The Sync Document SID
 */
/* jshint ignore:end */
DocumentPermissionList = function DocumentPermissionList(version, serviceSid,
                                                          documentSid) {
  /* jshint ignore:start */
  /**
   * @function documentPermissions
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext}
   */
  /* jshint ignore:end */
  function DocumentPermissionListInstance(sid) {
    return DocumentPermissionListInstance.get(sid);
  }

  DocumentPermissionListInstance._version = version;
  // Path Solution
  DocumentPermissionListInstance._solution = {serviceSid: serviceSid, documentSid: documentSid};
  DocumentPermissionListInstance._uri = `/Services/${serviceSid}/Documents/${documentSid}/Permissions`;
  /* jshint ignore:start */
  /**
   * Streams DocumentPermissionInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists DocumentPermissionInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of DocumentPermissionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new DocumentPermissionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of DocumentPermissionInstance records from the
   * API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new DocumentPermissionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a document_permission
   *
   * @function get
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @param {string} identity -
   *          The application-defined string that uniquely identifies the User's Document Permission resource to fetch
   *
   * @returns {Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext}
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.get = function get(identity) {
    return new DocumentPermissionContext(
      this._version,
      this._solution.serviceSid,
      this._solution.documentSid,
      identity
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DocumentPermissionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DocumentPermissionListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return DocumentPermissionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DocumentPermissionPage
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DocumentPermissionSolution} solution - Path solution
 *
 * @returns DocumentPermissionPage
 */
/* jshint ignore:end */
DocumentPermissionPage = function DocumentPermissionPage(version, response,
                                                          solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DocumentPermissionPage.prototype, Page.prototype);
DocumentPermissionPage.prototype.constructor = DocumentPermissionPage;

/* jshint ignore:start */
/**
 * Build an instance of DocumentPermissionInstance
 *
 * @function getInstance
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionPage#
 *
 * @param {DocumentPermissionPayload} payload - Payload response from the API
 *
 * @returns DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionPage.prototype.getInstance = function getInstance(payload) {
  return new DocumentPermissionInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.documentSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentPermissionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DocumentPermissionPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DocumentPermissionContext
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 * @property {string} documentSid - The Sync Document SID
 * @property {string} identity -
 *          The identity of the user to whom the Sync Document Permission applies
 * @property {boolean} read - Read access
 * @property {boolean} write - Write access
 * @property {boolean} manage - Manage access
 * @property {string} url -
 *          The absolute URL of the Sync Document Permission resource
 *
 * @param {V1} version - Version of the resource
 * @param {DocumentPermissionPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 * @param {sid} documentSid - The Sync Document SID
 * @param {string} identity -
 *          The application-defined string that uniquely identifies the User's Document Permission resource to fetch
 */
/* jshint ignore:end */
DocumentPermissionInstance = function DocumentPermissionInstance(version,
    payload, serviceSid, documentSid, identity) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.documentSid = payload.document_sid; // jshint ignore:line
  this.identity = payload.identity; // jshint ignore:line
  this.read = payload.read; // jshint ignore:line
  this.write = payload.write; // jshint ignore:line
  this.manage = payload.manage; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {
    serviceSid: serviceSid,
    documentSid: documentSid,
    identity: identity || this.identity,
  };
};

Object.defineProperty(DocumentPermissionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DocumentPermissionContext(
          this._version,
          this._solution.serviceSid,
          this._solution.documentSid,
          this._solution.identity
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a DocumentPermissionInstance
 *
 * @function fetch
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a DocumentPermissionInstance
 *
 * @function remove
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * update a DocumentPermissionInstance
 *
 * @function update
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionInstance#
 *
 * @param {object} opts - Options for request
 * @param {boolean} opts.read - Read access
 * @param {boolean} opts.write - Write access
 * @param {boolean} opts.manage - Manage access
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentPermissionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DocumentPermissionInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DocumentPermissionContext
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Sync Service with the Document Permission resource to fetch
 * @param {sid_like} documentSid -
 *          The SID of the Sync Document with the Document Permission resource to fetch
 * @param {string} identity -
 *          The application-defined string that uniquely identifies the User's Document Permission resource to fetch
 */
/* jshint ignore:end */
DocumentPermissionContext = function DocumentPermissionContext(version,
    serviceSid, documentSid, identity) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, documentSid: documentSid, identity: identity, };
  this._uri = `/Services/${serviceSid}/Documents/${documentSid}/Permissions/${identity}`;
};

/* jshint ignore:start */
/**
 * fetch a DocumentPermissionInstance
 *
 * @function fetch
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new DocumentPermissionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.documentSid,
      this._solution.identity
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a DocumentPermissionInstance
 *
 * @function remove
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a DocumentPermissionInstance
 *
 * @function update
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext#
 *
 * @param {object} opts - Options for request
 * @param {boolean} opts.read - Read access
 * @param {boolean} opts.write - Write access
 * @param {boolean} opts.manage - Manage access
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentPermissionInstance
 */
/* jshint ignore:end */
DocumentPermissionContext.prototype.update = function update(opts, callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['read'])) {
    throw new Error('Required parameter "opts[\'read\']" missing.');
  }
  if (_.isUndefined(opts['write'])) {
    throw new Error('Required parameter "opts[\'write\']" missing.');
  }
  if (_.isUndefined(opts['manage'])) {
    throw new Error('Required parameter "opts[\'manage\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({
    'Read': serialize.bool(_.get(opts, 'read')),
    'Write': serialize.bool(_.get(opts, 'write')),
    'Manage': serialize.bool(_.get(opts, 'manage'))
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new DocumentPermissionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.documentSid,
      this._solution.identity
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentPermissionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DocumentPermissionContext.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DocumentPermissionList: DocumentPermissionList,
  DocumentPermissionPage: DocumentPermissionPage,
  DocumentPermissionInstance: DocumentPermissionInstance,
  DocumentPermissionContext: DocumentPermissionContext
};
