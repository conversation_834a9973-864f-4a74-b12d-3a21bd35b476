'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var BundleCopyList = require('./bundle/bundleCopy').BundleCopyList;
var EvaluationList = require('./bundle/evaluation').EvaluationList;
var ItemAssignmentList = require('./bundle/itemAssignment').ItemAssignmentList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var ReplaceItemsList = require('./bundle/replaceItems').ReplaceItemsList;
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var BundleList;
var BundlePage;
var BundleInstance;
var BundleContext;

/* jshint ignore:start */
/**
 * Initialize the BundleList
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList
 *
 * @param {Twilio.Numbers.V2} version - Version of the resource
 */
/* jshint ignore:end */
BundleList = function BundleList(version) {
  /* jshint ignore:start */
  /**
   * @function bundles
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext}
   */
  /* jshint ignore:end */
  function BundleListInstance(sid) {
    return BundleListInstance.get(sid);
  }

  BundleListInstance._version = version;
  // Path Solution
  BundleListInstance._solution = {};
  BundleListInstance._uri = `/RegulatoryCompliance/Bundles`;
  /* jshint ignore:start */
  /**
   * create a BundleInstance
   *
   * @function create
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.friendlyName -
   *          The string that you assigned to describe the resource
   * @param {string} opts.email - The email address
   * @param {string} [opts.statusCallback] -
   *          The URL we call to inform your application of status changes.
   * @param {string} [opts.regulationSid] - The unique string of a regulation.
   * @param {string} [opts.isoCountry] - The ISO country code of the country
   * @param {bundle.end_user_type} [opts.endUserType] -
   *          The type of End User of the Bundle resource
   * @param {string} [opts.numberType] - The type of phone number
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed BundleInstance
   */
  /* jshint ignore:end */
  BundleListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['friendlyName'])) {
      throw new Error('Required parameter "opts[\'friendlyName\']" missing.');
    }
    if (_.isUndefined(opts['email'])) {
      throw new Error('Required parameter "opts[\'email\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'FriendlyName': _.get(opts, 'friendlyName'),
      'Email': _.get(opts, 'email'),
      'StatusCallback': _.get(opts, 'statusCallback'),
      'RegulationSid': _.get(opts, 'regulationSid'),
      'IsoCountry': _.get(opts, 'isoCountry'),
      'EndUserType': _.get(opts, 'endUserType'),
      'NumberType': _.get(opts, 'numberType')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BundleInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams BundleInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {object} [opts] - Options for request
   * @param {bundle.status} [opts.status] -
   *          The verification status of the Bundle resource
   * @param {string} [opts.friendlyName] -
   *          The string that you assigned to describe the resource
   * @param {string} [opts.regulationSid] - The unique string of a regulation.
   * @param {string} [opts.isoCountry] - The ISO country code of the country
   * @param {string} [opts.numberType] - The type of phone number
   * @param {boolean} [opts.hasValidUntilDate] -
   *          Indicates that the Bundle is a valid Bundle until a specified expiration date.
   * @param {bundle.sort_by} [opts.sortBy] -
   *          Can be `valid-until` or `date-updated`. Defaults to `date-created`.
   * @param {bundle.sort_direction} [opts.sortDirection] -
   *          Default is `DESC`. Can be `ASC` or `DESC`.
   * @param {Date} [opts.validUntilDateBefore] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDate] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDateAfter] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  BundleListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists BundleInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {object} [opts] - Options for request
   * @param {bundle.status} [opts.status] -
   *          The verification status of the Bundle resource
   * @param {string} [opts.friendlyName] -
   *          The string that you assigned to describe the resource
   * @param {string} [opts.regulationSid] - The unique string of a regulation.
   * @param {string} [opts.isoCountry] - The ISO country code of the country
   * @param {string} [opts.numberType] - The type of phone number
   * @param {boolean} [opts.hasValidUntilDate] -
   *          Indicates that the Bundle is a valid Bundle until a specified expiration date.
   * @param {bundle.sort_by} [opts.sortBy] -
   *          Can be `valid-until` or `date-updated`. Defaults to `date-created`.
   * @param {bundle.sort_direction} [opts.sortDirection] -
   *          Default is `DESC`. Can be `ASC` or `DESC`.
   * @param {Date} [opts.validUntilDateBefore] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDate] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDateAfter] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BundleListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of BundleInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {object} [opts] - Options for request
   * @param {bundle.status} [opts.status] -
   *          The verification status of the Bundle resource
   * @param {string} [opts.friendlyName] -
   *          The string that you assigned to describe the resource
   * @param {string} [opts.regulationSid] - The unique string of a regulation.
   * @param {string} [opts.isoCountry] - The ISO country code of the country
   * @param {string} [opts.numberType] - The type of phone number
   * @param {boolean} [opts.hasValidUntilDate] -
   *          Indicates that the Bundle is a valid Bundle until a specified expiration date.
   * @param {bundle.sort_by} [opts.sortBy] -
   *          Can be `valid-until` or `date-updated`. Defaults to `date-created`.
   * @param {bundle.sort_direction} [opts.sortDirection] -
   *          Default is `DESC`. Can be `ASC` or `DESC`.
   * @param {Date} [opts.validUntilDateBefore] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDate] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {Date} [opts.validUntilDateAfter] -
   *          Date to filter Bundles having their `valid_until_date` before or after the specified date. Can be `ValidUntilDate>=` or `ValidUntilDate<=`. Both can be used in conjunction as well.
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BundleListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'Status': _.get(opts, 'status'),
      'FriendlyName': _.get(opts, 'friendlyName'),
      'RegulationSid': _.get(opts, 'regulationSid'),
      'IsoCountry': _.get(opts, 'isoCountry'),
      'NumberType': _.get(opts, 'numberType'),
      'HasValidUntilDate': serialize.bool(_.get(opts, 'hasValidUntilDate')),
      'SortBy': _.get(opts, 'sortBy'),
      'SortDirection': _.get(opts, 'sortDirection'),
      'ValidUntilDate<': serialize.iso8601DateTime(_.get(opts, 'validUntilDateBefore')),
      'ValidUntilDate': serialize.iso8601DateTime(_.get(opts, 'validUntilDate')),
      'ValidUntilDate>': serialize.iso8601DateTime(_.get(opts, 'validUntilDateAfter')),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BundlePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of BundleInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BundleListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new BundlePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a bundle
   *
   * @function get
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @param {string} sid - The unique string that identifies the resource.
   *
   * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext}
   */
  /* jshint ignore:end */
  BundleListInstance.get = function get(sid) {
    return new BundleContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BundleListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BundleListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return BundleListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BundlePage
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundlePage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BundleSolution} solution - Path solution
 *
 * @returns BundlePage
 */
/* jshint ignore:end */
BundlePage = function BundlePage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BundlePage.prototype, Page.prototype);
BundlePage.prototype.constructor = BundlePage;

/* jshint ignore:start */
/**
 * Build an instance of BundleInstance
 *
 * @function getInstance
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundlePage#
 *
 * @param {BundlePayload} payload - Payload response from the API
 *
 * @returns BundleInstance
 */
/* jshint ignore:end */
BundlePage.prototype.getInstance = function getInstance(payload) {
  return new BundleInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundlePage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BundlePage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BundlePage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BundleContext
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance
 *
 * @property {string} sid - The unique string that identifies the resource.
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} regulationSid - The unique string of a regulation.
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {bundle.status} status -
 *          The verification status of the Bundle resource
 * @property {Date} validUntil -
 *          The ISO 8601 date and time in GMT when the resource will be valid until.
 * @property {string} email - The email address
 * @property {string} statusCallback -
 *          The URL we call to inform your application of status changes.
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the Bundle resource
 * @property {string} links - The URLs of the Assigned Items of the Bundle resource
 *
 * @param {V2} version - Version of the resource
 * @param {BundlePayload} payload - The instance payload
 * @param {sid} sid - The unique string that identifies the resource.
 */
/* jshint ignore:end */
BundleInstance = function BundleInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.regulationSid = payload.regulation_sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.validUntil = deserialize.iso8601DateTime(payload.valid_until); // jshint ignore:line
  this.email = payload.email; // jshint ignore:line
  this.statusCallback = payload.status_callback; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(BundleInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BundleContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a BundleInstance
 *
 * @function fetch
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a BundleInstance
 *
 * @function update
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {bundle.status} [opts.status] -
 *          The verification status of the Bundle resource
 * @param {string} [opts.statusCallback] -
 *          The URL we call to inform your application of status changes.
 * @param {string} [opts.friendlyName] -
 *          The string that you assigned to describe the resource
 * @param {string} [opts.email] - The email address
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * remove a BundleInstance
 *
 * @function remove
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Access the evaluations
 *
 * @function evaluations
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList}
 */
/* jshint ignore:end */
BundleInstance.prototype.evaluations = function evaluations() {
  return this._proxy.evaluations;
};

/* jshint ignore:start */
/**
 * Access the itemAssignments
 *
 * @function itemAssignments
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.ItemAssignmentList}
 */
/* jshint ignore:end */
BundleInstance.prototype.itemAssignments = function itemAssignments() {
  return this._proxy.itemAssignments;
};

/* jshint ignore:start */
/**
 * Access the bundleCopies
 *
 * @function bundleCopies
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.BundleCopyList}
 */
/* jshint ignore:end */
BundleInstance.prototype.bundleCopies = function bundleCopies() {
  return this._proxy.bundleCopies;
};

/* jshint ignore:start */
/**
 * Access the replaceItems
 *
 * @function replaceItems
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @returns {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.ReplaceItemsList}
 */
/* jshint ignore:end */
BundleInstance.prototype.replaceItems = function replaceItems() {
  return this._proxy.replaceItems;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BundleInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BundleInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BundleContext
 *
 * @constructor Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext
 *
 * @property {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.EvaluationList} evaluations -
 *          evaluations resource
 * @property {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.ItemAssignmentList} itemAssignments -
 *          itemAssignments resource
 * @property {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.BundleCopyList} bundleCopies -
 *          bundleCopies resource
 * @property {Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext.ReplaceItemsList} replaceItems -
 *          replaceItems resource
 *
 * @param {V2} version - Version of the resource
 * @param {sid} sid - The unique string that identifies the resource.
 */
/* jshint ignore:end */
BundleContext = function BundleContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/RegulatoryCompliance/Bundles/${sid}`;

  // Dependents
  this._evaluations = undefined;
  this._itemAssignments = undefined;
  this._bundleCopies = undefined;
  this._replaceItems = undefined;
};

/* jshint ignore:start */
/**
 * fetch a BundleInstance
 *
 * @function fetch
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BundleInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a BundleInstance
 *
 * @function update
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext#
 *
 * @param {object} [opts] - Options for request
 * @param {bundle.status} [opts.status] -
 *          The verification status of the Bundle resource
 * @param {string} [opts.statusCallback] -
 *          The URL we call to inform your application of status changes.
 * @param {string} [opts.friendlyName] -
 *          The string that you assigned to describe the resource
 * @param {string} [opts.email] - The email address
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'Status': _.get(opts, 'status'),
    'StatusCallback': _.get(opts, 'statusCallback'),
    'FriendlyName': _.get(opts, 'friendlyName'),
    'Email': _.get(opts, 'email')
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new BundleInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a BundleInstance
 *
 * @function remove
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BundleInstance
 */
/* jshint ignore:end */
BundleContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(BundleContext.prototype,
  'evaluations', {
    get: function() {
      if (!this._evaluations) {
        this._evaluations = new EvaluationList(this._version, this._solution.sid);
      }
      return this._evaluations;
    }
});

Object.defineProperty(BundleContext.prototype,
  'itemAssignments', {
    get: function() {
      if (!this._itemAssignments) {
        this._itemAssignments = new ItemAssignmentList(this._version, this._solution.sid);
      }
      return this._itemAssignments;
    }
});

Object.defineProperty(BundleContext.prototype,
  'bundleCopies', {
    get: function() {
      if (!this._bundleCopies) {
        this._bundleCopies = new BundleCopyList(this._version, this._solution.sid);
      }
      return this._bundleCopies;
    }
});

Object.defineProperty(BundleContext.prototype,
  'replaceItems', {
    get: function() {
      if (!this._replaceItems) {
        this._replaceItems = new ReplaceItemsList(this._version, this._solution.sid);
      }
      return this._replaceItems;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Numbers.V2.RegulatoryComplianceContext.BundleContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BundleContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BundleContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BundleList: BundleList,
  BundlePage: BundlePage,
  BundleInstance: BundleInstance,
  BundleContext: BundleContext
};
