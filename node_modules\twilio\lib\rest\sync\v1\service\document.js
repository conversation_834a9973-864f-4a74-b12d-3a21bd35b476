'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var DocumentPermissionList = require(
    './document/documentPermission').DocumentPermissionList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var DocumentList;
var DocumentPage;
var DocumentInstance;
var DocumentContext;

/* jshint ignore:start */
/**
 * Initialize the DocumentList
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentList
 *
 * @param {Twilio.Sync.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 */
/* jshint ignore:end */
DocumentList = function DocumentList(version, serviceSid) {
  /* jshint ignore:start */
  /**
   * @function documents
   * @memberof Twilio.Sync.V1.ServiceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Sync.V1.ServiceContext.DocumentContext}
   */
  /* jshint ignore:end */
  function DocumentListInstance(sid) {
    return DocumentListInstance.get(sid);
  }

  DocumentListInstance._version = version;
  // Path Solution
  DocumentListInstance._solution = {serviceSid: serviceSid};
  DocumentListInstance._uri = `/Services/${serviceSid}/Documents`;
  /* jshint ignore:start */
  /**
   * create a DocumentInstance
   *
   * @function create
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.uniqueName] -
   *          An application-defined string that uniquely identifies the Sync Document
   * @param {object} [opts.data] -
   *          A JSON string that represents an arbitrary, schema-less object that the Sync Document stores
   * @param {number} [opts.ttl] -
   *          How long, in seconds, before the Sync Document expires and is deleted
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed DocumentInstance
   */
  /* jshint ignore:end */
  DocumentListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'UniqueName': _.get(opts, 'uniqueName'),
      'Data': serialize.object(_.get(opts, 'data')),
      'Ttl': _.get(opts, 'ttl')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new DocumentInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams DocumentInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  DocumentListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists DocumentInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of DocumentInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new DocumentPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of DocumentInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  DocumentListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new DocumentPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a document
   *
   * @function get
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @param {string} sid - The SID of the Document resource to fetch
   *
   * @returns {Twilio.Sync.V1.ServiceContext.DocumentContext}
   */
  /* jshint ignore:end */
  DocumentListInstance.get = function get(sid) {
    return new DocumentContext(this._version, this._solution.serviceSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Sync.V1.ServiceContext.DocumentList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DocumentListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DocumentListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return DocumentListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DocumentPage
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DocumentSolution} solution - Path solution
 *
 * @returns DocumentPage
 */
/* jshint ignore:end */
DocumentPage = function DocumentPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DocumentPage.prototype, Page.prototype);
DocumentPage.prototype.constructor = DocumentPage;

/* jshint ignore:start */
/**
 * Build an instance of DocumentInstance
 *
 * @function getInstance
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentPage#
 *
 * @param {DocumentPayload} payload - Payload response from the API
 *
 * @returns DocumentInstance
 */
/* jshint ignore:end */
DocumentPage.prototype.getInstance = function getInstance(payload) {
  return new DocumentInstance(this._version, payload, this._solution.serviceSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DocumentPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DocumentContext
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} uniqueName -
 *          An application-defined string that uniquely identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 * @property {string} url - The absolute URL of the Document resource
 * @property {string} links - The URLs of resources related to the Sync Document
 * @property {string} revision -
 *          The current revision of the Sync Document, represented by a string identifier
 * @property {object} data -
 *          An arbitrary, schema-less object that the Sync Document stores
 * @property {Date} dateExpires -
 *          The ISO 8601 date and time in GMT when the Sync Document expires
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} createdBy - The identity of the Sync Document's creator
 *
 * @param {V1} version - Version of the resource
 * @param {DocumentPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Sync Service that the resource is associated with
 * @param {sid_like} sid - The SID of the Document resource to fetch
 */
/* jshint ignore:end */
DocumentInstance = function DocumentInstance(version, payload, serviceSid, sid)
                                              {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.uniqueName = payload.unique_name; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line
  this.revision = payload.revision; // jshint ignore:line
  this.data = payload.data; // jshint ignore:line
  this.dateExpires = deserialize.iso8601DateTime(payload.date_expires); // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.createdBy = payload.created_by; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sid: sid || this.sid, };
};

Object.defineProperty(DocumentInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DocumentContext(this._version, this._solution.serviceSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a DocumentInstance
 *
 * @function fetch
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a DocumentInstance
 *
 * @function remove
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * update a DocumentInstance
 *
 * @function update
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {object} [opts.data] -
 *          A JSON string that represents an arbitrary, schema-less object that the Sync Document stores
 * @param {number} [opts.ttl] -
 *          How long, in seconds, before the Document resource expires and is deleted
 * @param {string} [opts.ifMatch] - The If-Match HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Access the documentPermissions
 *
 * @function documentPermissions
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentInstance#
 *
 * @returns {Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList}
 */
/* jshint ignore:end */
DocumentInstance.prototype.documentPermissions = function documentPermissions()
    {
  return this._proxy.documentPermissions;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DocumentInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DocumentContext
 *
 * @constructor Twilio.Sync.V1.ServiceContext.DocumentContext
 *
 * @property {Twilio.Sync.V1.ServiceContext.DocumentContext.DocumentPermissionList} documentPermissions -
 *          documentPermissions resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Sync Service with the Document resource to fetch
 * @param {sid_like} sid - The SID of the Document resource to fetch
 */
/* jshint ignore:end */
DocumentContext = function DocumentContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Documents/${sid}`;

  // Dependents
  this._documentPermissions = undefined;
};

/* jshint ignore:start */
/**
 * fetch a DocumentInstance
 *
 * @function fetch
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new DocumentInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a DocumentInstance
 *
 * @function remove
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a DocumentInstance
 *
 * @function update
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext#
 *
 * @param {object} [opts] - Options for request
 * @param {object} [opts.data] -
 *          A JSON string that represents an arbitrary, schema-less object that the Sync Document stores
 * @param {number} [opts.ttl] -
 *          How long, in seconds, before the Document resource expires and is deleted
 * @param {string} [opts.ifMatch] - The If-Match HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DocumentInstance
 */
/* jshint ignore:end */
DocumentContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({'Data': serialize.object(_.get(opts, 'data')), 'Ttl': _.get(opts, 'ttl')});
  var headers = values.of({'If-Match': _.get(opts, 'ifMatch')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data, headers: headers});

  promise = promise.then(function(payload) {
    deferred.resolve(new DocumentInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(DocumentContext.prototype,
  'documentPermissions', {
    get: function() {
      if (!this._documentPermissions) {
        this._documentPermissions = new DocumentPermissionList(
          this._version,
          this._solution.serviceSid,
          this._solution.sid
        );
      }
      return this._documentPermissions;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Sync.V1.ServiceContext.DocumentContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DocumentContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DocumentContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DocumentList: DocumentList,
  DocumentPage: DocumentPage,
  DocumentInstance: DocumentInstance,
  DocumentContext: DocumentContext
};
