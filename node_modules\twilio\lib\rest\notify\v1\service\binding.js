'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var BindingList;
var BindingPage;
var BindingInstance;
var BindingContext;

/* jshint ignore:start */
/**
 * Initialize the BindingList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Notify.V1.ServiceContext.BindingList
 *
 * @param {Twilio.Notify.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 */
/* jshint ignore:end */
BindingList = function BindingList(version, serviceSid) {
  /* jshint ignore:start */
  /**
   * @function bindings
   * @memberof Twilio.Notify.V1.ServiceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Notify.V1.ServiceContext.BindingContext}
   */
  /* jshint ignore:end */
  function BindingListInstance(sid) {
    return BindingListInstance.get(sid);
  }

  BindingListInstance._version = version;
  // Path Solution
  BindingListInstance._solution = {serviceSid: serviceSid};
  BindingListInstance._uri = `/Services/${serviceSid}/Bindings`;
  /* jshint ignore:start */
  /**
   * create a BindingInstance
   *
   * @function create
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.identity -
   *          The `identity` value that identifies the new resource's User
   * @param {binding.binding_type} opts.bindingType - The type of the Binding
   * @param {string} opts.address - The channel-specific address
   * @param {string|list} [opts.tag] -
   *          A tag that can be used to select the Bindings to notify
   * @param {string} [opts.notificationProtocolVersion] -
   *          The protocol version to use to send the notification
   * @param {string} [opts.credentialSid] -
   *          The SID of the Credential resource to be used to send notifications to this Binding
   * @param {string} [opts.endpoint] - Deprecated
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed BindingInstance
   */
  /* jshint ignore:end */
  BindingListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['identity'])) {
      throw new Error('Required parameter "opts[\'identity\']" missing.');
    }
    if (_.isUndefined(opts['bindingType'])) {
      throw new Error('Required parameter "opts[\'bindingType\']" missing.');
    }
    if (_.isUndefined(opts['address'])) {
      throw new Error('Required parameter "opts[\'address\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'Identity': _.get(opts, 'identity'),
      'BindingType': _.get(opts, 'bindingType'),
      'Address': _.get(opts, 'address'),
      'Tag': serialize.map(_.get(opts, 'tag'), function(e) { return e; }),
      'NotificationProtocolVersion': _.get(opts, 'notificationProtocolVersion'),
      'CredentialSid': _.get(opts, 'credentialSid'),
      'Endpoint': _.get(opts, 'endpoint')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BindingInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams BindingInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.startDate] -
   *          Only include usage that has occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include usage that occurred on or before this date
   * @param {string|list} [opts.identity] -
   *          The `identity` value of the resources to read
   * @param {string|list} [opts.tag] -
   *          Only list Bindings that have all of the specified Tags
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  BindingListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists BindingInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.startDate] -
   *          Only include usage that has occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include usage that occurred on or before this date
   * @param {string|list} [opts.identity] -
   *          The `identity` value of the resources to read
   * @param {string|list} [opts.tag] -
   *          Only list Bindings that have all of the specified Tags
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BindingListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of BindingInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.startDate] -
   *          Only include usage that has occurred on or after this date
   * @param {Date} [opts.endDate] -
   *          Only include usage that occurred on or before this date
   * @param {string|list} [opts.identity] -
   *          The `identity` value of the resources to read
   * @param {string|list} [opts.tag] -
   *          Only list Bindings that have all of the specified Tags
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BindingListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'StartDate': serialize.iso8601Date(_.get(opts, 'startDate')),
      'EndDate': serialize.iso8601Date(_.get(opts, 'endDate')),
      'Identity': serialize.map(_.get(opts, 'identity'), function(e) { return e; }),
      'Tag': serialize.map(_.get(opts, 'tag'), function(e) { return e; }),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BindingPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of BindingInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BindingListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new BindingPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a binding
   *
   * @function get
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Notify.V1.ServiceContext.BindingContext}
   */
  /* jshint ignore:end */
  BindingListInstance.get = function get(sid) {
    return new BindingContext(this._version, this._solution.serviceSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Notify.V1.ServiceContext.BindingList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BindingListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BindingListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return BindingListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BindingPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Notify.V1.ServiceContext.BindingPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BindingSolution} solution - Path solution
 *
 * @returns BindingPage
 */
/* jshint ignore:end */
BindingPage = function BindingPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BindingPage.prototype, Page.prototype);
BindingPage.prototype.constructor = BindingPage;

/* jshint ignore:start */
/**
 * Build an instance of BindingInstance
 *
 * @function getInstance
 * @memberof Twilio.Notify.V1.ServiceContext.BindingPage#
 *
 * @param {BindingPayload} payload - Payload response from the API
 *
 * @returns BindingInstance
 */
/* jshint ignore:end */
BindingPage.prototype.getInstance = function getInstance(payload) {
  return new BindingInstance(this._version, payload, this._solution.serviceSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Notify.V1.ServiceContext.BindingPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BindingPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BindingPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BindingContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Notify.V1.ServiceContext.BindingInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @property {string} credentialSid -
 *          The SID of the Credential resource to be used to send notifications to this Binding
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} notificationProtocolVersion -
 *          The protocol version to use to send the notification
 * @property {string} endpoint - Deprecated
 * @property {string} identity -
 *          The `identity` value that identifies the new resource's User
 * @property {string} bindingType - The type of the Binding
 * @property {string} address - The channel-specific address
 * @property {string} tags - The list of tags associated with this Binding
 * @property {string} url - The absolute URL of the Binding resource
 * @property {string} links - The URLs of related resources
 *
 * @param {V1} version - Version of the resource
 * @param {BindingPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
BindingInstance = function BindingInstance(version, payload, serviceSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.credentialSid = payload.credential_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.notificationProtocolVersion = payload.notification_protocol_version; // jshint ignore:line
  this.endpoint = payload.endpoint; // jshint ignore:line
  this.identity = payload.identity; // jshint ignore:line
  this.bindingType = payload.binding_type; // jshint ignore:line
  this.address = payload.address; // jshint ignore:line
  this.tags = payload.tags; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sid: sid || this.sid, };
};

Object.defineProperty(BindingInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BindingContext(this._version, this._solution.serviceSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a BindingInstance
 *
 * @function fetch
 * @memberof Twilio.Notify.V1.ServiceContext.BindingInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BindingInstance
 */
/* jshint ignore:end */
BindingInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a BindingInstance
 *
 * @function remove
 * @memberof Twilio.Notify.V1.ServiceContext.BindingInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BindingInstance
 */
/* jshint ignore:end */
BindingInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Notify.V1.ServiceContext.BindingInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BindingInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BindingInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BindingContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Notify.V1.ServiceContext.BindingContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} serviceSid - The SID of the Service to fetch the resource from
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
BindingContext = function BindingContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Bindings/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a BindingInstance
 *
 * @function fetch
 * @memberof Twilio.Notify.V1.ServiceContext.BindingContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BindingInstance
 */
/* jshint ignore:end */
BindingContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BindingInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a BindingInstance
 *
 * @function remove
 * @memberof Twilio.Notify.V1.ServiceContext.BindingContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BindingInstance
 */
/* jshint ignore:end */
BindingContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Notify.V1.ServiceContext.BindingContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BindingContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BindingContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BindingList: BindingList,
  BindingPage: BindingPage,
  BindingInstance: BindingInstance,
  BindingContext: BindingContext
};
