'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./flexApi/V1');  /* jshint ignore:line */
var V2 = require('./flexApi/V2');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize flex_api domain
 *
 * @constructor Twilio.FlexApi
 *
 * @property {Twilio.FlexApi.V1} v1 - v1 version
 * @property {Twilio.FlexApi.V2} v2 - v2 version
 * @property {Twilio.FlexApi.V1.AssessmentsList} assessments - assessments resource
 * @property {Twilio.FlexApi.V1.ChannelList} channel - channel resource
 * @property {Twilio.FlexApi.V1.ConfigurationList} configuration -
 *          configuration resource
 * @property {Twilio.FlexApi.V1.FlexFlowList} flexFlow - flexFlow resource
 * @property {Twilio.FlexApi.V1.GoodDataList} goodData - goodData resource
 * @property {Twilio.FlexApi.V1.InteractionList} interaction - interaction resource
 * @property {Twilio.FlexApi.V1.UserRolesList} userRoles - userRoles resource
 * @property {Twilio.FlexApi.V1.WebChannelList} webChannel - webChannel resource
 * @property {Twilio.FlexApi.V2.WebChannelsList} webChannels - webChannels resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function FlexApi(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://flex-api.twilio.com');

  // Versions
  this._v1 = undefined;
  this._v2 = undefined;
}

_.extend(FlexApi.prototype, Domain.prototype);
FlexApi.prototype.constructor = FlexApi;

Object.defineProperty(FlexApi.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(FlexApi.prototype,
  'v2', {
    get: function() {
      this._v2 = this._v2 || new V2(this);
      return this._v2;
    }
});

Object.defineProperty(FlexApi.prototype,
  'assessments', {
    get: function() {
      return this.v1.assessments;
    }
});

Object.defineProperty(FlexApi.prototype,
  'channel', {
    get: function() {
      return this.v1.channel;
    }
});

Object.defineProperty(FlexApi.prototype,
  'configuration', {
    get: function() {
      return this.v1.configuration;
    }
});

Object.defineProperty(FlexApi.prototype,
  'flexFlow', {
    get: function() {
      return this.v1.flexFlow;
    }
});

Object.defineProperty(FlexApi.prototype,
  'goodData', {
    get: function() {
      return this.v1.goodData;
    }
});

Object.defineProperty(FlexApi.prototype,
  'interaction', {
    get: function() {
      return this.v1.interaction;
    }
});

Object.defineProperty(FlexApi.prototype,
  'userRoles', {
    get: function() {
      return this.v1.userRoles;
    }
});

Object.defineProperty(FlexApi.prototype,
  'webChannel', {
    get: function() {
      return this.v1.webChannel;
    }
});

Object.defineProperty(FlexApi.prototype,
  'webChannels', {
    get: function() {
      return this.v2.webChannels;
    }
});

module.exports = FlexApi;
