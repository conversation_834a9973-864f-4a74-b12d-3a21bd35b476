// Required External Modules
const express = require("express");
const bodyParser = require("body-parser");
const mongoose = require('mongoose');
const cors = require('cors');
// const compression = require('compression')
var verifyToken = require('./app/middleware/verifyToken.js').default;
const path = require('path');
require('dotenv').config();
var appt = require("./app/controllers/appointments.controllers");
var payment = require("./app/controllers/payment.controller");
var fs = require('fs');
var https = require('https');
// const cron = require('node-cron') 

// var options = {
// 	key: fs.readFileSync('/etc/ssl/new/reedapp_net_key.key'),
// 	cert: fs.readFileSync('/etc/ssl/new/reedapp_net.crt'),
// };

//App Variables
const app = express();
// app.use(compression())
const PORT = process.env.PORT;

global.__basedir = __dirname;

//Parse requests of content-type - application/json
app.use(cors());
//app.use(bodyParser.json({limit: '120mb'}));
app.use(express.json({limit: '50mb'}));
app.use(express.urlencoded({limit: '50mb'}))

//Parse requests of content-type - application/x-www-form-urlencoded
app.use(bodyParser.urlencoded({ extended: true }));

// Function to serve all static files
app.use(express.static(path.join(__dirname, 'app/public')));

//Allow cross origin
app.use(function (req, res, next) {
    res.header("Access-Control-Allow-origin", "*");
    res.header("Access-Control-Allow-Methods", "GET,PUT,POST,DELETE,OPTIONS");
    res.header("Access-Control-Allow-Headers", "Origin, X-requested-With,Content-Type,Accept");
    if (req.method === "OPTIONS") {
        return res.status(200).send();
    }
    next();
});

// Connect to Mongoose and set connection variable
//DB name ==> doctor-reed
mongoose.connect('mongodb+srv://priyadharshiniarumugam_db_user:<EMAIL>/doctor-reed', { useUnifiedTopology: true, useNewUrlParser: true, useFindAndModify: false, useCreateIndex: true });
var db = mongoose.connection;
// Added check for DB connection
if (!db)
    console.log("Error connecting db")
else
    console.log("Db connected successfully")

//Routes Defintions
app.get("/", (req, res) => {
    res.status(200).send({ message: "Welcome to Doctor Reed Application." });
});

//Login Access
// require("./app/routes/auth.routes.js")(app);
const log = console.log

// cron function
function cron(ms, fn) {
    function cb() {
        clearTimeout(timeout)
        timeout = setTimeout(cb, ms)
        appt.appt();
        appt.Reminder();
        // payment.PaymentFailure();
        fn()
    }
    let timeout = setTimeout(cb, ms)
    return () => { }
}
// setup cron job
cron(10 * 60 , () => { })

// Group by non authentication jobs     
var apiRoutesNoAuth = express.Router();
apiRoutesNoAuth.use(bodyParser.json());
apiRoutesNoAuth.use(bodyParser.urlencoded({ extended: true }));
// apiRoutes.use(verifyToken);
require("./app/routes/page.routes.js")(apiRoutesNoAuth);
app.use('/', apiRoutesNoAuth);

// Group by non authentication jobs
var apiRoutesNoAuth = express.Router();
apiRoutesNoAuth.use(bodyParser.json());
apiRoutesNoAuth.use(bodyParser.urlencoded({ extended: true }));
require("./app/routes/auth.routes.js")(apiRoutesNoAuth);
app.use('/api/', apiRoutesNoAuth);

// group by authentications
var apiRoutes = express.Router();
apiRoutes.use(bodyParser.json());
apiRoutes.use(bodyParser.urlencoded({ extended: true }));
// apiRoutes.use(verifyToken);
require("./app/routes/user.routes.js")(apiRoutes);
require("./app/routes/app_settings.routes.js")(apiRoutes);
require("./app/routes/resources.routes.js")(apiRoutes);
require("./app/routes/pets.routes.js")(apiRoutes);
require("./app/routes/appointments.routes.js")(apiRoutes);
require("./app/routes/products.routes.js")(apiRoutes);
require("./app/routes/cms.routes.js")(apiRoutes);
require("./app/routes/covertus.routes.js")(apiRoutes);
require("./app/routes/order.routes.js")(apiRoutes);
require('./app/routes/payment.routes.js')(apiRoutes);
require('./app/routes/log.routes.js')(apiRoutes);
app.use('/api/v1', apiRoutes);

//Serevr Activation
app.listen(PORT, () => {
    console.log(`Server is running on port ${PORT}.`);
});
// var server = https.createServer(options, app).listen(PORT, function(){
// 	console.log(`Server is running on port ${PORT}.`);
// });
