'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var DomainCertsList;
var DomainCertsPage;
var DomainCertsInstance;
var DomainCertsContext;

/* jshint ignore:start */
/**
 * Initialize the DomainCertsList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.DomainCertsList
 *
 * @param {Twilio.Messaging.V1} version - Version of the resource
 */
/* jshint ignore:end */
DomainCertsList = function DomainCertsList(version) {
  /* jshint ignore:start */
  /**
   * @function domainCerts
   * @memberof Twilio.Messaging.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Messaging.V1.DomainCertsContext}
   */
  /* jshint ignore:end */
  function DomainCertsListInstance(sid) {
    return DomainCertsListInstance.get(sid);
  }

  DomainCertsListInstance._version = version;
  // Path Solution
  DomainCertsListInstance._solution = {};
  /* jshint ignore:start */
  /**
   * Constructs a domain_certs
   *
   * @function get
   * @memberof Twilio.Messaging.V1.DomainCertsList#
   *
   * @param {string} domainSid -
   *          Unique string used to identify the domain that this certificate should be associated with.
   *
   * @returns {Twilio.Messaging.V1.DomainCertsContext}
   */
  /* jshint ignore:end */
  DomainCertsListInstance.get = function get(domainSid) {
    return new DomainCertsContext(this._version, domainSid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Messaging.V1.DomainCertsList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DomainCertsListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DomainCertsListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return DomainCertsListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DomainCertsPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.DomainCertsPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DomainCertsSolution} solution - Path solution
 *
 * @returns DomainCertsPage
 */
/* jshint ignore:end */
DomainCertsPage = function DomainCertsPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DomainCertsPage.prototype, Page.prototype);
DomainCertsPage.prototype.constructor = DomainCertsPage;

/* jshint ignore:start */
/**
 * Build an instance of DomainCertsInstance
 *
 * @function getInstance
 * @memberof Twilio.Messaging.V1.DomainCertsPage#
 *
 * @param {DomainCertsPayload} payload - Payload response from the API
 *
 * @returns DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsPage.prototype.getInstance = function getInstance(payload) {
  return new DomainCertsInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DomainCertsPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DomainCertsPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DomainCertsPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DomainCertsContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.DomainCertsInstance
 *
 * @property {string} domainSid -
 *          The unique string that we created to identify the Domain resource.
 * @property {Date} dateUpdated - Date that this Domain was last updated.
 * @property {Date} dateExpires - Expiration date for your private certificate.
 * @property {Date} dateCreated - Date this Domain SID was created.
 * @property {string} domainName - Full url path for this domain.
 * @property {string} certificateSid -
 *          The unique string that we created to identify this Certificate resource.
 * @property {string} url - The url
 * @property {boolean} validated - Certificate validation field
 *
 * @param {V1} version - Version of the resource
 * @param {DomainCertsPayload} payload - The instance payload
 * @param {sid} domainSid -
 *          Unique string used to identify the domain that this certificate should be associated with.
 */
/* jshint ignore:end */
DomainCertsInstance = function DomainCertsInstance(version, payload, domainSid)
                                                    {
  this._version = version;

  // Marshaled Properties
  this.domainSid = payload.domain_sid; // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.dateExpires = deserialize.iso8601DateTime(payload.date_expires); // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.domainName = payload.domain_name; // jshint ignore:line
  this.certificateSid = payload.certificate_sid; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.validated = payload.validated; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {domainSid: domainSid || this.domainSid, };
};

Object.defineProperty(DomainCertsInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DomainCertsContext(this._version, this._solution.domainSid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * update a DomainCertsInstance
 *
 * @function update
 * @memberof Twilio.Messaging.V1.DomainCertsInstance#
 *
 * @param {object} opts - Options for request
 * @param {string} opts.tlsCert -
 *          Certificate and private key information for this domain.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * fetch a DomainCertsInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.DomainCertsInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a DomainCertsInstance
 *
 * @function remove
 * @memberof Twilio.Messaging.V1.DomainCertsInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DomainCertsInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DomainCertsInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DomainCertsInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DomainCertsContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.DomainCertsContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} domainSid -
 *          Unique string used to identify the domain that this certificate should be associated with.
 */
/* jshint ignore:end */
DomainCertsContext = function DomainCertsContext(version, domainSid) {
  this._version = version;

  // Path Solution
  this._solution = {domainSid: domainSid, };
  this._uri = `/LinkShortening/Domains/${domainSid}/Certificate`;
};

/* jshint ignore:start */
/**
 * update a DomainCertsInstance
 *
 * @function update
 * @memberof Twilio.Messaging.V1.DomainCertsContext#
 *
 * @param {object} opts - Options for request
 * @param {string} opts.tlsCert -
 *          Certificate and private key information for this domain.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsContext.prototype.update = function update(opts, callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['tlsCert'])) {
    throw new Error('Required parameter "opts[\'tlsCert\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({'TlsCert': _.get(opts, 'tlsCert')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new DomainCertsInstance(this._version, payload, this._solution.domainSid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * fetch a DomainCertsInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.DomainCertsContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new DomainCertsInstance(this._version, payload, this._solution.domainSid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a DomainCertsInstance
 *
 * @function remove
 * @memberof Twilio.Messaging.V1.DomainCertsContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DomainCertsInstance
 */
/* jshint ignore:end */
DomainCertsContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DomainCertsContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DomainCertsContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DomainCertsContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DomainCertsList: DomainCertsList,
  DomainCertsPage: DomainCertsPage,
  DomainCertsInstance: DomainCertsInstance,
  DomainCertsContext: DomainCertsContext
};
