'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var BuildStatusList = require('./build/buildStatus').BuildStatusList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var BuildList;
var BuildPage;
var BuildInstance;
var BuildContext;

/* jshint ignore:start */
/**
 * Initialize the BuildList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildList
 *
 * @param {Twilio.Serverless.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 */
/* jshint ignore:end */
BuildList = function BuildList(version, serviceSid) {
  /* jshint ignore:start */
  /**
   * @function builds
   * @memberof Twilio.Serverless.V1.ServiceContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.BuildContext}
   */
  /* jshint ignore:end */
  function BuildListInstance(sid) {
    return BuildListInstance.get(sid);
  }

  BuildListInstance._version = version;
  // Path Solution
  BuildListInstance._solution = {serviceSid: serviceSid};
  BuildListInstance._uri = `/Services/${serviceSid}/Builds`;
  /* jshint ignore:start */
  /**
   * Streams BuildInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  BuildListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists BuildInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BuildListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of BuildInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BuildListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BuildPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of BuildInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  BuildListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new BuildPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * create a BuildInstance
   *
   * @function create
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {object} [opts] - Options for request
   * @param {string|list} [opts.assetVersions] -
   *          The list of Asset Version resource SIDs to include in the Build
   * @param {string|list} [opts.functionVersions] -
   *          The list of the Function Version resource SIDs to include in the Build
   * @param {string} [opts.dependencies] -
   *          A list of objects that describe the Dependencies included in the Build
   * @param {string} [opts.runtime] -
   *          The Runtime version that will be used to run the Build.
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed BuildInstance
   */
  /* jshint ignore:end */
  BuildListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'AssetVersions': serialize.map(_.get(opts, 'assetVersions'), function(e) { return e; }),
      'FunctionVersions': serialize.map(_.get(opts, 'functionVersions'), function(e) { return e; }),
      'Dependencies': _.get(opts, 'dependencies'),
      'Runtime': _.get(opts, 'runtime')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new BuildInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a build
   *
   * @function get
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @param {string} sid - The SID of the Build resource to fetch
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.BuildContext}
   */
  /* jshint ignore:end */
  BuildListInstance.get = function get(sid) {
    return new BuildContext(this._version, this._solution.serviceSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Serverless.V1.ServiceContext.BuildList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  BuildListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  BuildListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return BuildListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the BuildPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {BuildSolution} solution - Path solution
 *
 * @returns BuildPage
 */
/* jshint ignore:end */
BuildPage = function BuildPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(BuildPage.prototype, Page.prototype);
BuildPage.prototype.constructor = BuildPage;

/* jshint ignore:start */
/**
 * Build an instance of BuildInstance
 *
 * @function getInstance
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildPage#
 *
 * @param {BuildPayload} payload - Payload response from the API
 *
 * @returns BuildInstance
 */
/* jshint ignore:end */
BuildPage.prototype.getInstance = function getInstance(payload) {
  return new BuildInstance(this._version, payload, this._solution.serviceSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BuildPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BuildContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildInstance
 *
 * @property {string} sid - The unique string that identifies the Build resource
 * @property {string} accountSid -
 *          The SID of the Account that created the Build resource
 * @property {string} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 * @property {build.status} status - The status of the Build
 * @property {object} assetVersions -
 *          The list of Asset Version resource SIDs that are included in the Build
 * @property {object} functionVersions -
 *          The list of Function Version resource SIDs that are included in the Build
 * @property {object} dependencies -
 *          A list of objects that describe the Dependencies included in the Build
 * @property {build.runtime} runtime -
 *          The Runtime version that will be used to run the Build.
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the Build resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the Build resource was last updated
 * @property {string} url - The absolute URL of the Build resource
 * @property {string} links - The links
 *
 * @param {V1} version - Version of the resource
 * @param {BuildPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the Build resource is associated with
 * @param {sid} sid - The SID of the Build resource to fetch
 */
/* jshint ignore:end */
BuildInstance = function BuildInstance(version, payload, serviceSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.assetVersions = payload.asset_versions; // jshint ignore:line
  this.functionVersions = payload.function_versions; // jshint ignore:line
  this.dependencies = payload.dependencies; // jshint ignore:line
  this.runtime = payload.runtime; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, sid: sid || this.sid, };
};

Object.defineProperty(BuildInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new BuildContext(this._version, this._solution.serviceSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a BuildInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildInstance
 */
/* jshint ignore:end */
BuildInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a BuildInstance
 *
 * @function remove
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildInstance
 */
/* jshint ignore:end */
BuildInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Access the buildStatus
 *
 * @function buildStatus
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildInstance#
 *
 * @returns {Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusList}
 */
/* jshint ignore:end */
BuildInstance.prototype.buildStatus = function buildStatus() {
  return this._proxy.buildStatus;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

BuildInstance.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the BuildContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.BuildContext
 *
 * @property {Twilio.Serverless.V1.ServiceContext.BuildContext.BuildStatusList} buildStatus -
 *          buildStatus resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Service to fetch the Build resource from
 * @param {sid} sid - The SID of the Build resource to fetch
 */
/* jshint ignore:end */
BuildContext = function BuildContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Builds/${sid}`;

  // Dependents
  this._buildStatus = undefined;
};

/* jshint ignore:start */
/**
 * fetch a BuildInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildInstance
 */
/* jshint ignore:end */
BuildContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new BuildInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a BuildInstance
 *
 * @function remove
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed BuildInstance
 */
/* jshint ignore:end */
BuildContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(BuildContext.prototype,
  'buildStatus', {
    get: function() {
      if (!this._buildStatus) {
        this._buildStatus = new BuildStatusList(
          this._version,
          this._solution.serviceSid,
          this._solution.sid
        );
      }
      return this._buildStatus;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.BuildContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
BuildContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

BuildContext.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  BuildList: BuildList,
  BuildPage: BuildPage,
  BuildInstance: BuildInstance,
  BuildContext: BuildContext
};
