//Required external modules
var mongoose = require('mongoose');
var doctor = require('../../models/CMS-Model/doctor.models')
var user = require('../user.models');

// Setup schema
var appointment_scheduleSchema = mongoose.Schema({
    doctor_id: [{ type: mongoose.Schema.Types.ObjectId, ref: doctor }],
    mon: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    tue: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    wed: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    thu: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    fri: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    sat: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    sun: { mode: { type: String, enum: ['IN-PERSON', 'VIDEO'], default: 'IN-PERSON' }, time: [], slot: [] },
    date: { time: [], slot: [] }
}, { timestamps: true });

// Export Module model
module.exports = mongoose.model('appointment_schedule', appointment_scheduleSchema);

module.exports.get = function (callback, limit) {
    Module.find(callback).limit(limit);
}