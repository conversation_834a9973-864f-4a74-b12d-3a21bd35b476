'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var IpAccessControlListList;
var IpAccessControlListPage;
var IpAccessControlListInstance;
var IpAccessControlListContext;

/* jshint ignore:start */
/**
 * Initialize the IpAccessControlListList
 *
 * @constructor Twilio.Trunking.V1.TrunkContext.IpAccessControlListList
 *
 * @param {Twilio.Trunking.V1} version - Version of the resource
 * @param {string} trunkSid - The SID of the Trunk the resource is associated with
 */
/* jshint ignore:end */
IpAccessControlListList = function IpAccessControlListList(version, trunkSid) {
  /* jshint ignore:start */
  /**
   * @function ipAccessControlLists
   * @memberof Twilio.Trunking.V1.TrunkContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext}
   */
  /* jshint ignore:end */
  function IpAccessControlListListInstance(sid) {
    return IpAccessControlListListInstance.get(sid);
  }

  IpAccessControlListListInstance._version = version;
  // Path Solution
  IpAccessControlListListInstance._solution = {trunkSid: trunkSid};
  IpAccessControlListListInstance._uri = `/Trunks/${trunkSid}/IpAccessControlLists`;
  /* jshint ignore:start */
  /**
   * create a IpAccessControlListInstance
   *
   * @function create
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.ipAccessControlListSid -
   *          The SID of the IP Access Control List that you want to associate with the trunk
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed IpAccessControlListInstance
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['ipAccessControlListSid'])) {
      throw new Error('Required parameter "opts[\'ipAccessControlListSid\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({'IpAccessControlListSid': _.get(opts, 'ipAccessControlListSid')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new IpAccessControlListInstance(
        this._version,
        payload,
        this._solution.trunkSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams IpAccessControlListInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists IpAccessControlListInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of IpAccessControlListInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new IpAccessControlListPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of IpAccessControlListInstance records from the
   * API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.getPage = function getPage(targetUrl, callback)
                                                              {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new IpAccessControlListPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a ip_access_control_list
   *
   * @function get
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext}
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.get = function get(sid) {
    return new IpAccessControlListContext(this._version, this._solution.trunkSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  IpAccessControlListListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  IpAccessControlListListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return IpAccessControlListListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the IpAccessControlListPage
 *
 * @constructor Twilio.Trunking.V1.TrunkContext.IpAccessControlListPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {IpAccessControlListSolution} solution - Path solution
 *
 * @returns IpAccessControlListPage
 */
/* jshint ignore:end */
IpAccessControlListPage = function IpAccessControlListPage(version, response,
                                                            solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(IpAccessControlListPage.prototype, Page.prototype);
IpAccessControlListPage.prototype.constructor = IpAccessControlListPage;

/* jshint ignore:start */
/**
 * Build an instance of IpAccessControlListInstance
 *
 * @function getInstance
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListPage#
 *
 * @param {IpAccessControlListPayload} payload - Payload response from the API
 *
 * @returns IpAccessControlListInstance
 */
/* jshint ignore:end */
IpAccessControlListPage.prototype.getInstance = function getInstance(payload) {
  return new IpAccessControlListInstance(this._version, payload, this._solution.trunkSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
IpAccessControlListPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

IpAccessControlListPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the IpAccessControlListContext
 *
 * @constructor Twilio.Trunking.V1.TrunkContext.IpAccessControlListInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} trunkSid -
 *          The SID of the Trunk the resource is associated with
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the resource
 *
 * @param {V1} version - Version of the resource
 * @param {IpAccessControlListPayload} payload - The instance payload
 * @param {sid} trunkSid - The SID of the Trunk the resource is associated with
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
IpAccessControlListInstance = function IpAccessControlListInstance(version,
    payload, trunkSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.trunkSid = payload.trunk_sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {trunkSid: trunkSid, sid: sid || this.sid, };
};

Object.defineProperty(IpAccessControlListInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new IpAccessControlListContext(
          this._version,
          this._solution.trunkSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a IpAccessControlListInstance
 *
 * @function fetch
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed IpAccessControlListInstance
 */
/* jshint ignore:end */
IpAccessControlListInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a IpAccessControlListInstance
 *
 * @function remove
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed IpAccessControlListInstance
 */
/* jshint ignore:end */
IpAccessControlListInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
IpAccessControlListInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

IpAccessControlListInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the IpAccessControlListContext
 *
 * @constructor Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} trunkSid -
 *          The SID of the Trunk from which to fetch the IP Access Control List
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
IpAccessControlListContext = function IpAccessControlListContext(version,
    trunkSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {trunkSid: trunkSid, sid: sid, };
  this._uri = `/Trunks/${trunkSid}/IpAccessControlLists/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a IpAccessControlListInstance
 *
 * @function fetch
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed IpAccessControlListInstance
 */
/* jshint ignore:end */
IpAccessControlListContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new IpAccessControlListInstance(
      this._version,
      payload,
      this._solution.trunkSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a IpAccessControlListInstance
 *
 * @function remove
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed IpAccessControlListInstance
 */
/* jshint ignore:end */
IpAccessControlListContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trunking.V1.TrunkContext.IpAccessControlListContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
IpAccessControlListContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

IpAccessControlListContext.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  IpAccessControlListList: IpAccessControlListList,
  IpAccessControlListPage: IpAccessControlListPage,
  IpAccessControlListInstance: IpAccessControlListInstance,
  IpAccessControlListContext: IpAccessControlListContext
};
