'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var ExternalCampaignList;
var ExternalCampaignPage;
var ExternalCampaignInstance;

/* jshint ignore:start */
/**
 * Initialize the ExternalCampaignList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.ExternalCampaignList
 *
 * @param {Twilio.Messaging.V1} version - Version of the resource
 */
/* jshint ignore:end */
ExternalCampaignList = function ExternalCampaignList(version) {
  /* jshint ignore:start */
  /**
   * @function externalCampaign
   * @memberof Twilio.Messaging.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Messaging.V1.ExternalCampaignContext}
   */
  /* jshint ignore:end */
  function ExternalCampaignListInstance(sid) {
    return ExternalCampaignListInstance.get(sid);
  }

  ExternalCampaignListInstance._version = version;
  // Path Solution
  ExternalCampaignListInstance._solution = {};
  ExternalCampaignListInstance._uri = `/Services/PreregisteredUsa2p`;
  /* jshint ignore:start */
  /**
   * create a ExternalCampaignInstance
   *
   * @function create
   * @memberof Twilio.Messaging.V1.ExternalCampaignList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.campaignId - ID of the preregistered campaign.
   * @param {string} opts.messagingServiceSid -
   *          The SID of the Messaging Service the resource is associated with
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed ExternalCampaignInstance
   */
  /* jshint ignore:end */
  ExternalCampaignListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['campaignId'])) {
      throw new Error('Required parameter "opts[\'campaignId\']" missing.');
    }
    if (_.isUndefined(opts['messagingServiceSid'])) {
      throw new Error('Required parameter "opts[\'messagingServiceSid\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'CampaignId': _.get(opts, 'campaignId'),
      'MessagingServiceSid': _.get(opts, 'messagingServiceSid')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ExternalCampaignInstance(this._version, payload));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Messaging.V1.ExternalCampaignList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ExternalCampaignListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ExternalCampaignListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return ExternalCampaignListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ExternalCampaignPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.ExternalCampaignPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ExternalCampaignSolution} solution - Path solution
 *
 * @returns ExternalCampaignPage
 */
/* jshint ignore:end */
ExternalCampaignPage = function ExternalCampaignPage(version, response,
                                                      solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ExternalCampaignPage.prototype, Page.prototype);
ExternalCampaignPage.prototype.constructor = ExternalCampaignPage;

/* jshint ignore:start */
/**
 * Build an instance of ExternalCampaignInstance
 *
 * @function getInstance
 * @memberof Twilio.Messaging.V1.ExternalCampaignPage#
 *
 * @param {ExternalCampaignPayload} payload - Payload response from the API
 *
 * @returns ExternalCampaignInstance
 */
/* jshint ignore:end */
ExternalCampaignPage.prototype.getInstance = function getInstance(payload) {
  return new ExternalCampaignInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.ExternalCampaignPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExternalCampaignPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExternalCampaignPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExternalCampaignContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Messaging.V1.ExternalCampaignInstance
 *
 * @property {string} sid -
 *          The unique string that identifies a US A2P Compliance resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} campaignId - ID of the preregistered campaign.
 * @property {string} messagingServiceSid -
 *          The SID of the Messaging Service the resource is associated with
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 *
 * @param {V1} version - Version of the resource
 * @param {ExternalCampaignPayload} payload - The instance payload
 */
/* jshint ignore:end */
ExternalCampaignInstance = function ExternalCampaignInstance(version, payload) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.campaignId = payload.campaign_id; // jshint ignore:line
  this.messagingServiceSid = payload.messaging_service_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {};
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.ExternalCampaignInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExternalCampaignInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExternalCampaignInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ExternalCampaignList: ExternalCampaignList,
  ExternalCampaignPage: ExternalCampaignPage,
  ExternalCampaignInstance: ExternalCampaignInstance
};
