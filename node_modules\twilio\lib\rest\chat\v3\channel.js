'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var ChannelList;
var ChannelPage;
var ChannelInstance;
var ChannelContext;

/* jshint ignore:start */
/**
 * Initialize the ChannelList
 *
 * @constructor Twilio.Chat.V3.ChannelList
 *
 * @param {Twilio.Chat.V3} version - Version of the resource
 */
/* jshint ignore:end */
ChannelList = function ChannelList(version) {
  /* jshint ignore:start */
  /**
   * @function channels
   * @memberof Twilio.Chat.V3#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Chat.V3.ChannelContext}
   */
  /* jshint ignore:end */
  function ChannelListInstance(sid) {
    return ChannelListInstance.get(sid);
  }

  ChannelListInstance._version = version;
  // Path Solution
  ChannelListInstance._solution = {};
  /* jshint ignore:start */
  /**
   * Constructs a channel
   *
   * @function get
   * @memberof Twilio.Chat.V3.ChannelList#
   *
   * @param {string} serviceSid - Service Sid.
   * @param {string} sid - A string that uniquely identifies this Channel.
   *
   * @returns {Twilio.Chat.V3.ChannelContext}
   */
  /* jshint ignore:end */
  ChannelListInstance.get = function get(serviceSid, sid) {
    return new ChannelContext(this._version, serviceSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Chat.V3.ChannelList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ChannelListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ChannelListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return ChannelListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ChannelPage
 *
 * @constructor Twilio.Chat.V3.ChannelPage
 *
 * @param {V3} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ChannelSolution} solution - Path solution
 *
 * @returns ChannelPage
 */
/* jshint ignore:end */
ChannelPage = function ChannelPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ChannelPage.prototype, Page.prototype);
ChannelPage.prototype.constructor = ChannelPage;

/* jshint ignore:start */
/**
 * Build an instance of ChannelInstance
 *
 * @function getInstance
 * @memberof Twilio.Chat.V3.ChannelPage#
 *
 * @param {ChannelPayload} payload - Payload response from the API
 *
 * @returns ChannelInstance
 */
/* jshint ignore:end */
ChannelPage.prototype.getInstance = function getInstance(payload) {
  return new ChannelInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V3.ChannelPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ChannelPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ChannelPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ChannelContext
 *
 * @constructor Twilio.Chat.V3.ChannelInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {string} uniqueName -
 *          An application-defined string that uniquely identifies the resource
 * @property {string} attributes -
 *          The JSON string that stores application-specific data
 * @property {channel.channel_type} type -
 *          The visibility of the channel. Can be: `public` or `private`
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} createdBy - The identity of the User that created the channel
 * @property {number} membersCount - The number of Members in the Channel
 * @property {number} messagesCount -
 *          The number of Messages that have been passed in the Channel
 * @property {string} messagingServiceSid -
 *          The unique ID of the Messaging Service this channel belongs to.
 * @property {string} url - The absolute URL of the Channel resource
 *
 * @param {V3} version - Version of the resource
 * @param {ChannelPayload} payload - The instance payload
 * @param {sid} serviceSid - Service Sid.
 * @param {sid_like} sid - A string that uniquely identifies this Channel.
 */
/* jshint ignore:end */
ChannelInstance = function ChannelInstance(version, payload, serviceSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.uniqueName = payload.unique_name; // jshint ignore:line
  this.attributes = payload.attributes; // jshint ignore:line
  this.type = payload.type; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.createdBy = payload.created_by; // jshint ignore:line
  this.membersCount = deserialize.integer(payload.members_count); // jshint ignore:line
  this.messagesCount = deserialize.integer(payload.messages_count); // jshint ignore:line
  this.messagingServiceSid = payload.messaging_service_sid; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid || this.serviceSid, sid: sid || this.sid, };
};

Object.defineProperty(ChannelInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ChannelContext(this._version, this._solution.serviceSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * update a ChannelInstance
 *
 * @function update
 * @memberof Twilio.Chat.V3.ChannelInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {channel.channel_type} [opts.type] -
 *          The Type for this Channel to migrate to.
 * @param {string} [opts.messagingServiceSid] -
 *          The unique ID of the Messaging Service this channel belongs to.
 * @param {channel.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ChannelInstance
 */
/* jshint ignore:end */
ChannelInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V3.ChannelInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ChannelInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ChannelInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ChannelContext
 *
 * @constructor Twilio.Chat.V3.ChannelContext
 *
 * @param {V3} version - Version of the resource
 * @param {sid} serviceSid - Service Sid.
 * @param {sid_like} sid - A string that uniquely identifies this Channel.
 */
/* jshint ignore:end */
ChannelContext = function ChannelContext(version, serviceSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Channels/${sid}`;
};

/* jshint ignore:start */
/**
 * update a ChannelInstance
 *
 * @function update
 * @memberof Twilio.Chat.V3.ChannelContext#
 *
 * @param {object} [opts] - Options for request
 * @param {channel.channel_type} [opts.type] -
 *          The Type for this Channel to migrate to.
 * @param {string} [opts.messagingServiceSid] -
 *          The unique ID of the Messaging Service this channel belongs to.
 * @param {channel.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ChannelInstance
 */
/* jshint ignore:end */
ChannelContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'Type': _.get(opts, 'type'),
    'MessagingServiceSid': _.get(opts, 'messagingServiceSid')
  });
  var headers = values.of({'X-Twilio-Webhook-Enabled': _.get(opts, 'xTwilioWebhookEnabled')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data, headers: headers});

  promise = promise.then(function(payload) {
    deferred.resolve(new ChannelInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V3.ChannelContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ChannelContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ChannelContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ChannelList: ChannelList,
  ChannelPage: ChannelPage,
  ChannelInstance: ChannelInstance,
  ChannelContext: ChannelContext
};
