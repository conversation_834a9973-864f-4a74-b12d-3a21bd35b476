'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var DeactivationsList;
var DeactivationsPage;
var DeactivationsInstance;
var DeactivationsContext;

/* jshint ignore:start */
/**
 * Initialize the DeactivationsList
 *
 * @constructor Twilio.Messaging.V1.DeactivationsList
 *
 * @param {Twilio.Messaging.V1} version - Version of the resource
 */
/* jshint ignore:end */
DeactivationsList = function DeactivationsList(version) {
  /* jshint ignore:start */
  /**
   * @function deactivations
   * @memberof Twilio.Messaging.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Messaging.V1.DeactivationsContext}
   */
  /* jshint ignore:end */
  function DeactivationsListInstance(sid) {
    return DeactivationsListInstance.get(sid);
  }

  DeactivationsListInstance._version = version;
  // Path Solution
  DeactivationsListInstance._solution = {};
  /* jshint ignore:start */
  /**
   * Constructs a deactivations
   *
   * @function get
   * @memberof Twilio.Messaging.V1.DeactivationsList#
   *
   * @returns {Twilio.Messaging.V1.DeactivationsContext}
   */
  /* jshint ignore:end */
  DeactivationsListInstance.get = function get() {
    return new DeactivationsContext(this._version);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Messaging.V1.DeactivationsList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  DeactivationsListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  DeactivationsListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return DeactivationsListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the DeactivationsPage
 *
 * @constructor Twilio.Messaging.V1.DeactivationsPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {DeactivationsSolution} solution - Path solution
 *
 * @returns DeactivationsPage
 */
/* jshint ignore:end */
DeactivationsPage = function DeactivationsPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(DeactivationsPage.prototype, Page.prototype);
DeactivationsPage.prototype.constructor = DeactivationsPage;

/* jshint ignore:start */
/**
 * Build an instance of DeactivationsInstance
 *
 * @function getInstance
 * @memberof Twilio.Messaging.V1.DeactivationsPage#
 *
 * @param {DeactivationsPayload} payload - Payload response from the API
 *
 * @returns DeactivationsInstance
 */
/* jshint ignore:end */
DeactivationsPage.prototype.getInstance = function getInstance(payload) {
  return new DeactivationsInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DeactivationsPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeactivationsPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DeactivationsPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeactivationsContext
 *
 * @constructor Twilio.Messaging.V1.DeactivationsInstance
 *
 * @property {string} redirectTo - Redirect url to the list of deactivated numbers.
 *
 * @param {V1} version - Version of the resource
 * @param {DeactivationsPayload} payload - The instance payload
 */
/* jshint ignore:end */
DeactivationsInstance = function DeactivationsInstance(version, payload) {
  this._version = version;

  // Marshaled Properties
  this.redirectTo = payload.redirect_to; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {};
};

Object.defineProperty(DeactivationsInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new DeactivationsContext(this._version);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a DeactivationsInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.DeactivationsInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {Date} [opts.date] - The date to retrieve deactivated numbers for.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeactivationsInstance
 */
/* jshint ignore:end */
DeactivationsInstance.prototype.fetch = function fetch(opts, callback) {
  return this._proxy.fetch(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DeactivationsInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeactivationsInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

DeactivationsInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the DeactivationsContext
 *
 * @constructor Twilio.Messaging.V1.DeactivationsContext
 *
 * @param {V1} version - Version of the resource
 */
/* jshint ignore:end */
DeactivationsContext = function DeactivationsContext(version) {
  this._version = version;

  // Path Solution
  this._solution = {};
  this._uri = `/Deactivations`;
};

/* jshint ignore:start */
/**
 * fetch a DeactivationsInstance
 *
 * @function fetch
 * @memberof Twilio.Messaging.V1.DeactivationsContext#
 *
 * @param {object} [opts] - Options for request
 * @param {Date} [opts.date] - The date to retrieve deactivated numbers for.
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed DeactivationsInstance
 */
/* jshint ignore:end */
DeactivationsContext.prototype.fetch = function fetch(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({'Date': serialize.iso8601Date(_.get(opts, 'date'))});

  var promise = this._version.fetch({uri: this._uri, method: 'GET', params: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new DeactivationsInstance(this._version, payload));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Messaging.V1.DeactivationsContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
DeactivationsContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

DeactivationsContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  DeactivationsList: DeactivationsList,
  DeactivationsPage: DeactivationsPage,
  DeactivationsInstance: DeactivationsInstance,
  DeactivationsContext: DeactivationsContext
};
