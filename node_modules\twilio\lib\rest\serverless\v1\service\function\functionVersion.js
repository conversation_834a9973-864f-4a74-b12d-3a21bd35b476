'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var FunctionVersionContentList = require(
    './functionVersion/functionVersionContent').FunctionVersionContentList;
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var FunctionVersionList;
var FunctionVersionPage;
var FunctionVersionInstance;
var FunctionVersionContext;

/* jshint ignore:start */
/**
 * Initialize the FunctionVersionList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList
 *
 * @param {Twilio.Serverless.V1} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the Function Version resource is associated with
 * @param {string} functionSid -
 *          The SID of the Function resource that is the parent of the Function Version resource
 */
/* jshint ignore:end */
FunctionVersionList = function FunctionVersionList(version, serviceSid,
                                                    functionSid) {
  /* jshint ignore:start */
  /**
   * @function functionVersions
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext}
   */
  /* jshint ignore:end */
  function FunctionVersionListInstance(sid) {
    return FunctionVersionListInstance.get(sid);
  }

  FunctionVersionListInstance._version = version;
  // Path Solution
  FunctionVersionListInstance._solution = {serviceSid: serviceSid, functionSid: functionSid};
  FunctionVersionListInstance._uri = `/Services/${serviceSid}/Functions/${functionSid}/Versions`;
  /* jshint ignore:start */
  /**
   * Streams FunctionVersionInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists FunctionVersionInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of FunctionVersionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FunctionVersionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of FunctionVersionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new FunctionVersionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a function_version
   *
   * @function get
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @param {string} sid -
   *          The SID that identifies the Function Version resource to fetch
   *
   * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext}
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.get = function get(sid) {
    return new FunctionVersionContext(
      this._version,
      this._solution.serviceSid,
      this._solution.functionSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FunctionVersionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FunctionVersionListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return FunctionVersionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FunctionVersionPage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FunctionVersionSolution} solution - Path solution
 *
 * @returns FunctionVersionPage
 */
/* jshint ignore:end */
FunctionVersionPage = function FunctionVersionPage(version, response, solution)
                                                    {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FunctionVersionPage.prototype, Page.prototype);
FunctionVersionPage.prototype.constructor = FunctionVersionPage;

/* jshint ignore:start */
/**
 * Build an instance of FunctionVersionInstance
 *
 * @function getInstance
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionPage#
 *
 * @param {FunctionVersionPayload} payload - Payload response from the API
 *
 * @returns FunctionVersionInstance
 */
/* jshint ignore:end */
FunctionVersionPage.prototype.getInstance = function getInstance(payload) {
  return new FunctionVersionInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.functionSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionVersionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FunctionVersionPage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FunctionVersionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionInstance
 *
 * @property {string} sid -
 *          The unique string that identifies the Function Version resource
 * @property {string} accountSid -
 *          The SID of the Account that created the Function Version resource
 * @property {string} serviceSid -
 *          The SID of the Service that the Function Version resource is associated with
 * @property {string} functionSid -
 *          The SID of the Function resource that is the parent of the Function Version resource
 * @property {string} path -
 *          The URL-friendly string by which the Function Version resource can be referenced
 * @property {function_version.visibility} visibility -
 *          The access control that determines how the Function Version resource can be accessed
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the Function Version resource was created
 * @property {string} url - The absolute URL of the Function Version resource
 * @property {string} links - The links
 *
 * @param {V1} version - Version of the resource
 * @param {FunctionVersionPayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the Function Version resource is associated with
 * @param {sid} functionSid -
 *          The SID of the Function resource that is the parent of the Function Version resource
 * @param {sid} sid -
 *          The SID that identifies the Function Version resource to fetch
 */
/* jshint ignore:end */
FunctionVersionInstance = function FunctionVersionInstance(version, payload,
                                                            serviceSid,
                                                            functionSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.functionSid = payload.function_sid; // jshint ignore:line
  this.path = payload.path; // jshint ignore:line
  this.visibility = payload.visibility; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, functionSid: functionSid, sid: sid || this.sid, };
};

Object.defineProperty(FunctionVersionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new FunctionVersionContext(
          this._version,
          this._solution.serviceSid,
          this._solution.functionSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a FunctionVersionInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionVersionInstance
 */
/* jshint ignore:end */
FunctionVersionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Access the functionVersionContent
 *
 * @function functionVersionContent
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionInstance#
 *
 * @returns {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext.FunctionVersionContentList}
 */
/* jshint ignore:end */
FunctionVersionInstance.prototype.functionVersionContent = function
    functionVersionContent() {
  return this._proxy.functionVersionContent;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionVersionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FunctionVersionInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FunctionVersionContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext
 *
 * @property {Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext.FunctionVersionContentList} functionVersionContent -
 *          functionVersionContent resource
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} serviceSid -
 *          The SID of the Service to fetch the Function Version resource from
 * @param {sid} functionSid -
 *          The SID of the function that is the parent of the Function Version resource to fetch
 * @param {sid} sid -
 *          The SID that identifies the Function Version resource to fetch
 */
/* jshint ignore:end */
FunctionVersionContext = function FunctionVersionContext(version, serviceSid,
                                                          functionSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, functionSid: functionSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Functions/${functionSid}/Versions/${sid}`;

  // Dependents
  this._functionVersionContent = undefined;
};

/* jshint ignore:start */
/**
 * fetch a FunctionVersionInstance
 *
 * @function fetch
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FunctionVersionInstance
 */
/* jshint ignore:end */
FunctionVersionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new FunctionVersionInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.functionSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(FunctionVersionContext.prototype,
  'functionVersionContent', {
    get: function() {
      if (!this._functionVersionContent) {
        this._functionVersionContent = new FunctionVersionContentList(
          this._version,
          this._solution.serviceSid,
          this._solution.functionSid,
          this._solution.sid
        );
      }
      return this._functionVersionContent;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Serverless.V1.ServiceContext.FunctionContext.FunctionVersionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
FunctionVersionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

FunctionVersionContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FunctionVersionList: FunctionVersionList,
  FunctionVersionPage: FunctionVersionPage,
  FunctionVersionInstance: FunctionVersionInstance,
  FunctionVersionContext: FunctionVersionContext
};
