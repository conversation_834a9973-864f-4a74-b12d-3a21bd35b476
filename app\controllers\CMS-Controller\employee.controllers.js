const Admin = require('../../models/admin.models')
const jwt = require("jsonwebtoken");
const Mail = require("../../models/mail.js");
const bcrypt = require('bcrypt');
const SChedule = require('../../models/CMS-Model/appointment_schedule.models.js');
 
/******** Employee *********/
/**
 * Create New Employee
 */
exports.CreateEmployee = async (req, res) => {
    if (!req.body) {
        res.status(200).send({ 'code': 100, data: 'Field Can`t Empty' });
    }
    const EmailVerify = await Admin.findOne({ email: req.body.email });
    if (EmailVerify)
        return res.status(200).send({ code: 100, message: 'Email address is exists!!!.' });
    const employee = new Admin(req.body);
    employee.password = bcrypt.hashSync('12345678', 10);

    const payload = { Finish: "TOPSCRETE" };
    //Create json web token
    const token = jwt.sign(payload, "randomString", { expiresIn: '1d' });
    employee.resetPasswordToken = token;
   // employee.pin = bcrypt.hashSync('1234', 10);
    //Save user to database
    const SavedEmp = await employee.save();
    const data = {
        name: SavedEmp.name,
        email: SavedEmp.email,
        tokens: token
    }

    await Mail.EmployeeComfirm(data, async function (err2, res2) { });

    const schedule = new SChedule({ doctor_id: SavedEmp._id });
    if (req.body.role_name === 'Doctor' || req.body.role_name === 'DOCTOR' || req.body.role_name === 'doctor' || req.body.role_name === 'Doctors')
        await schedule.save();

    return res.status(200).send({ 'code': 200, message: "Account Activation link sent to your mail,please check" });
}


/**
 * Get All Employee List
 */
exports.GetAllEmployee = async (req, res) => {
    var search = req.query.search;
    var location = req.query.location? req.query.location : '';
    var skip = req.query.skip ? parseInt(req.query.skip) : 0;
    var limit = req.query.limit ? parseInt(req.query.limit) : 10;
    var filter = { $or: [{ "name": { $regex: search, $options: "i" } }, { "email": { $regex: search, $options: "i" } }, { "role_name": { $regex: search, $options: "i" } }]  }
    if(location!=""){
        filter = { location: location, $or: [{ "name": { $regex: search, $options: "i" } }, { "email": { $regex: search, $options: "i" } }, { "role_name": { $regex: search, $options: "i" } }]  }
    }
    
    const data = await Admin.find(filter).populate('role_id').sort({ _id: -1 }).skip(skip).limit(limit)
    const count = await Admin.countDocuments(filter)
    return res.status(200).send({ 'code': 200, data, count, message: 'successfully retrieve employee list.' })
}


/**
 * Get All Employee in True Active Status
 */
exports.GetActiveEmployee = async (req, res) => {
    var search = req.query.search;
    var skip = req.query.skip ? parseInt(req.query.skip) : 0;
    var limit = req.query.limit ? parseInt(req.query.limit) : 10;
    const data = await Admin.find({ acc_activation: true }).sort({ _id: -1 }).skip(skip).limit(limit);
    const count = await Admin.countDocuments({ acc_activation: true })
    return res.status(200).send({ 'code': 200, data, count, message: 'successfully retrieve employee list.' })
}


/**
 * Get Particular Employee by using employee id
 */
exports.GetEmployee = async (req, res) => {
    const data = await Admin.findOne({ '_id': req.params.Employee_id }).populate('role_id')
    if (data) return res.status(200).send({ 'code': 200, data, message: 'successfully retrieve employee.' });
    else return res.status(200).send({ 'code': 100, message: 'Invalid employee id' });
}

/**
 * Edit or Update Particular Employee by using Employee id
 */
exports.EditEmployee = async (req, res) => {
    if (req.body.password) {
        req.body.password = bcrypt.hashSync(req.body.password, 10)
    }
    if (req.body.pin)
        req.body.pin = bcrypt.hashSync(req.body.pin, 10)
    const data = await Admin.findByIdAndUpdate(req.params.Employee_id, req.body, { new: true })
    if (!data) return res.status(200).send({ 'code': 100, message: "No Employee Found" });
    return res.status(200).send({ 'code': 200, data, message: 'Employee updated succesfully' })

}

/**
 * Delete Particular Employee by using Employee id
 */
exports.DeleteEmployee = async (req, res) => {
    const data = await Admin.findByIdAndRemove(req.params.Employee_id)
    if (!data) return res.status(200).send({ 'code': 100, message: "No Employee Found" });
    return res.status(200).send({ 'code': 200, message: "Employee deleted successfully!" })
}

/**
 * Password Confirm
 */
exports.emp_get_forgotPassword = (req, res) => {
    // console.log(req.query.token)
    Admin.findOne({ 'resetPasswordToken': req.query.token })
        .then((data) => {
            // console.log('-->', data)
            if (data) res.status(200).send({ 'code': 200, data })
            else res.status(200).send({ 'code': 100, message: "Invalid Request!.." })
        })
        .catch((err) => { res.status(200).send({ 'code': 100, err }) })
}

/**
* Update password 
**/
exports.emp_forgotPassword = async (req, res) => {
    const user = await Admin.findOne({ 'resetPasswordToken': req.body.token });
    if (user) {
        if (req.body.newPassword === req.body.confirmPassword) {
            const hash_password = bcrypt.hashSync(req.body.newPassword, 10);
            const resetPasswordToken = null;
            const result = await Admin.updateMany({ '_id': user._id }, { 'password': hash_password, 'resetPasswordToken': resetPasswordToken, 'status': true }, { new: true })
            if (!result) return res.status(200).send({ code: 100, message: 'Some Thing error' });
            else return res.status(200).send({ code: 200, message: 'Your password changed successfully' });
        } else return res.status(200).send({ code: 100, message: 'Passwords do not match' });
    } else return res.status(200).send({ code: 100, message: 'Password reset token is invalid or has expired.' })
};

async function makeid(length) {
    var result = '';
    var characters = '0123456789';
    var charactersLength = characters.length;
    for (var i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() *
            charactersLength));
    }
    return result;
}
