'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require(
    '../../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../../base/values');  /* jshint ignore:line */

var MessageList;
var MessagePage;
var MessageInstance;
var MessageContext;

/* jshint ignore:start */
/**
 * Initialize the MessageList
 *
 * @constructor Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList
 *
 * @param {Twilio.Chat.V2} version - Version of the resource
 * @param {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {string} channelSid -
 *          The SID of the Channel the Message resource belongs to
 */
/* jshint ignore:end */
MessageList = function MessageList(version, serviceSid, channelSid) {
  /* jshint ignore:start */
  /**
   * @function messages
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext}
   */
  /* jshint ignore:end */
  function MessageListInstance(sid) {
    return MessageListInstance.get(sid);
  }

  MessageListInstance._version = version;
  // Path Solution
  MessageListInstance._solution = {serviceSid: serviceSid, channelSid: channelSid};
  MessageListInstance._uri = `/Services/${serviceSid}/Channels/${channelSid}/Messages`;
  /* jshint ignore:start */
  /**
   * create a MessageInstance
   *
   * @function create
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.from] - The Identity of the new message's author
   * @param {string} [opts.attributes] -
   *          A valid JSON string that contains application-specific data
   * @param {Date} [opts.dateCreated] -
   *          The ISO 8601 date and time in GMT when the resource was created
   * @param {Date} [opts.dateUpdated] -
   *          The ISO 8601 date and time in GMT when the resource was updated
   * @param {string} [opts.lastUpdatedBy] -
   *          The Identity of the User who last updated the Message
   * @param {string} [opts.body] - The message to send to the channel
   * @param {string} [opts.mediaSid] -
   *         The Media Sid to be attached to the new Message
   * @param {message.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
   *          The X-Twilio-Webhook-Enabled HTTP request header
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed MessageInstance
   */
  /* jshint ignore:end */
  MessageListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'From': _.get(opts, 'from'),
      'Attributes': _.get(opts, 'attributes'),
      'DateCreated': serialize.iso8601DateTime(_.get(opts, 'dateCreated')),
      'DateUpdated': serialize.iso8601DateTime(_.get(opts, 'dateUpdated')),
      'LastUpdatedBy': _.get(opts, 'lastUpdatedBy'),
      'Body': _.get(opts, 'body'),
      'MediaSid': _.get(opts, 'mediaSid')
    });
    var headers = values.of({'X-Twilio-Webhook-Enabled': _.get(opts, 'xTwilioWebhookEnabled')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data, headers: headers});

    promise = promise.then(function(payload) {
      deferred.resolve(new MessageInstance(
        this._version,
        payload,
        this._solution.serviceSid,
        this._solution.channelSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams MessageInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {object} [opts] - Options for request
   * @param {message.order_type} [opts.order] -
   *          The sort order of the returned messages
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  MessageListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists MessageInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {object} [opts] - Options for request
   * @param {message.order_type} [opts.order] -
   *          The sort order of the returned messages
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MessageListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of MessageInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {object} [opts] - Options for request
   * @param {message.order_type} [opts.order] -
   *          The sort order of the returned messages
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MessageListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'Order': _.get(opts, 'order'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new MessagePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of MessageInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  MessageListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new MessagePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a message
   *
   * @function get
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @param {string} sid - The SID of the Message resource to fetch
   *
   * @returns {Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext}
   */
  /* jshint ignore:end */
  MessageListInstance.get = function get(sid) {
    return new MessageContext(this._version, this._solution.serviceSid, this._solution.channelSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  MessageListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  MessageListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return MessageListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the MessagePage
 *
 * @constructor Twilio.Chat.V2.ServiceContext.ChannelContext.MessagePage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {MessageSolution} solution - Path solution
 *
 * @returns MessagePage
 */
/* jshint ignore:end */
MessagePage = function MessagePage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(MessagePage.prototype, Page.prototype);
MessagePage.prototype.constructor = MessagePage;

/* jshint ignore:start */
/**
 * Build an instance of MessageInstance
 *
 * @function getInstance
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessagePage#
 *
 * @param {MessagePayload} payload - Payload response from the API
 *
 * @returns MessageInstance
 */
/* jshint ignore:end */
MessagePage.prototype.getInstance = function getInstance(payload) {
  return new MessageInstance(
    this._version,
    payload,
    this._solution.serviceSid,
    this._solution.channelSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessagePage#
 *
 * @returns Object
 */
/* jshint ignore:end */
MessagePage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

MessagePage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the MessageContext
 *
 * @constructor Twilio.Chat.V2.ServiceContext.ChannelContext.MessageInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} attributes -
 *          The JSON string that stores application-specific data
 * @property {string} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @property {string} to - The SID of the Channel that the message was sent to
 * @property {string} channelSid -
 *          The SID of the Channel the Message resource belongs to
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT when the resource was last updated
 * @property {string} lastUpdatedBy -
 *          The Identity of the User who last updated the Message
 * @property {boolean} wasEdited -
 *          Whether the message has been edited since  it was created
 * @property {string} from - The Identity of the message's author
 * @property {string} body - The content of the message
 * @property {number} index - The index of the message within the Channel
 * @property {string} type - The Message type
 * @property {object} media -
 *          A Media object that describes the Message's media if attached; otherwise, null
 * @property {string} url - The absolute URL of the Message resource
 *
 * @param {V2} version - Version of the resource
 * @param {MessagePayload} payload - The instance payload
 * @param {sid} serviceSid -
 *          The SID of the Service that the resource is associated with
 * @param {sid} channelSid - The SID of the Channel the Message resource belongs to
 * @param {sid} sid - The SID of the Message resource to fetch
 */
/* jshint ignore:end */
MessageInstance = function MessageInstance(version, payload, serviceSid,
                                            channelSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.attributes = payload.attributes; // jshint ignore:line
  this.serviceSid = payload.service_sid; // jshint ignore:line
  this.to = payload.to; // jshint ignore:line
  this.channelSid = payload.channel_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.lastUpdatedBy = payload.last_updated_by; // jshint ignore:line
  this.wasEdited = payload.was_edited; // jshint ignore:line
  this.from = payload.from; // jshint ignore:line
  this.body = payload.body; // jshint ignore:line
  this.index = deserialize.integer(payload.index); // jshint ignore:line
  this.type = payload.type; // jshint ignore:line
  this.media = payload.media; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {serviceSid: serviceSid, channelSid: channelSid, sid: sid || this.sid, };
};

Object.defineProperty(MessageInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new MessageContext(
          this._version,
          this._solution.serviceSid,
          this._solution.channelSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a MessageInstance
 *
 * @function fetch
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a MessageInstance
 *
 * @function remove
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {message.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageInstance.prototype.remove = function remove(opts, callback) {
  return this._proxy.remove(opts, callback);
};

/* jshint ignore:start */
/**
 * update a MessageInstance
 *
 * @function update
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.body] - The message to send to the channel
 * @param {string} [opts.attributes] -
 *          A valid JSON string that contains application-specific data
 * @param {Date} [opts.dateCreated] -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @param {Date} [opts.dateUpdated] -
 *          The ISO 8601 date and time in GMT when the resource was updated
 * @param {string} [opts.lastUpdatedBy] -
 *          The Identity of the User who last updated the Message, if applicable
 * @param {string} [opts.from] - The Identity of the message's author
 * @param {message.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
MessageInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

MessageInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the MessageContext
 *
 * @constructor Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext
 *
 * @param {V2} version - Version of the resource
 * @param {sid} serviceSid - The SID of the Service to fetch the resource from
 * @param {sid_like} channelSid -
 *          The SID of the Channel the message to fetch belongs to
 * @param {sid} sid - The SID of the Message resource to fetch
 */
/* jshint ignore:end */
MessageContext = function MessageContext(version, serviceSid, channelSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {serviceSid: serviceSid, channelSid: channelSid, sid: sid, };
  this._uri = `/Services/${serviceSid}/Channels/${channelSid}/Messages/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a MessageInstance
 *
 * @function fetch
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new MessageInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.channelSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a MessageInstance
 *
 * @function remove
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext#
 *
 * @param {object} [opts] - Options for request
 * @param {message.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageContext.prototype.remove = function remove(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var headers = values.of({'X-Twilio-Webhook-Enabled': _.get(opts, 'xTwilioWebhookEnabled')});

  var promise = this._version.remove({uri: this._uri, method: 'DELETE', headers: headers});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a MessageInstance
 *
 * @function update
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.body] - The message to send to the channel
 * @param {string} [opts.attributes] -
 *          A valid JSON string that contains application-specific data
 * @param {Date} [opts.dateCreated] -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @param {Date} [opts.dateUpdated] -
 *          The ISO 8601 date and time in GMT when the resource was updated
 * @param {string} [opts.lastUpdatedBy] -
 *          The Identity of the User who last updated the Message, if applicable
 * @param {string} [opts.from] - The Identity of the message's author
 * @param {message.webhook_enabled_type} [opts.xTwilioWebhookEnabled] -
 *          The X-Twilio-Webhook-Enabled HTTP request header
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed MessageInstance
 */
/* jshint ignore:end */
MessageContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'Body': _.get(opts, 'body'),
    'Attributes': _.get(opts, 'attributes'),
    'DateCreated': serialize.iso8601DateTime(_.get(opts, 'dateCreated')),
    'DateUpdated': serialize.iso8601DateTime(_.get(opts, 'dateUpdated')),
    'LastUpdatedBy': _.get(opts, 'lastUpdatedBy'),
    'From': _.get(opts, 'from')
  });
  var headers = values.of({'X-Twilio-Webhook-Enabled': _.get(opts, 'xTwilioWebhookEnabled')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data, headers: headers});

  promise = promise.then(function(payload) {
    deferred.resolve(new MessageInstance(
      this._version,
      payload,
      this._solution.serviceSid,
      this._solution.channelSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Chat.V2.ServiceContext.ChannelContext.MessageContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
MessageContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

MessageContext.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  MessageList: MessageList,
  MessagePage: MessagePage,
  MessageInstance: MessageInstance,
  MessageContext: MessageContext
};
