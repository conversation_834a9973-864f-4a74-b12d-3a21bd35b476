'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var EsimProfileList;
var EsimProfilePage;
var EsimProfileInstance;
var EsimProfileContext;

/* jshint ignore:start */
/**
 * Initialize the EsimProfileList
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.EsimProfileList
 *
 * @param {Twilio.Supersim.V1} version - Version of the resource
 */
/* jshint ignore:end */
EsimProfileList = function EsimProfileList(version) {
  /* jshint ignore:start */
  /**
   * @function esimProfiles
   * @memberof Twilio.Supersim.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Supersim.V1.EsimProfileContext}
   */
  /* jshint ignore:end */
  function EsimProfileListInstance(sid) {
    return EsimProfileListInstance.get(sid);
  }

  EsimProfileListInstance._version = version;
  // Path Solution
  EsimProfileListInstance._solution = {};
  EsimProfileListInstance._uri = `/ESimProfiles`;
  /* jshint ignore:start */
  /**
   * create a EsimProfileInstance
   *
   * @function create
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.callbackUrl] -
   *          The URL we should call after we have sent when the status of the eSIM Profile changes
   * @param {string} [opts.callbackMethod] -
   *          The HTTP method we should use to call callback_url
   * @param {string} [opts.eid] -
   *          Identifier of the eUICC that will claim the eSIM Profile
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed EsimProfileInstance
   */
  /* jshint ignore:end */
  EsimProfileListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'CallbackUrl': _.get(opts, 'callbackUrl'),
      'CallbackMethod': _.get(opts, 'callbackMethod'),
      'Eid': _.get(opts, 'eid')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EsimProfileInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams EsimProfileInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.eid] -
   *          List the eSIM Profiles that have been associated with an EId
   * @param {string} [opts.simSid] -
   *          Find the eSIM Profile resource related to a Sim resource by providing the SIM SID
   * @param {esim_profile.status} [opts.status] -
   *          List the eSIM Profiles that are in a given status
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  EsimProfileListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists EsimProfileInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.eid] -
   *          List the eSIM Profiles that have been associated with an EId
   * @param {string} [opts.simSid] -
   *          Find the eSIM Profile resource related to a Sim resource by providing the SIM SID
   * @param {esim_profile.status} [opts.status] -
   *          List the eSIM Profiles that are in a given status
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EsimProfileListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of EsimProfileInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.eid] -
   *          List the eSIM Profiles that have been associated with an EId
   * @param {string} [opts.simSid] -
   *          Find the eSIM Profile resource related to a Sim resource by providing the SIM SID
   * @param {esim_profile.status} [opts.status] -
   *          List the eSIM Profiles that are in a given status
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EsimProfileListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'Eid': _.get(opts, 'eid'),
      'SimSid': _.get(opts, 'simSid'),
      'Status': _.get(opts, 'status'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new EsimProfilePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of EsimProfileInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  EsimProfileListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new EsimProfilePage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a esim_profile
   *
   * @function get
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @param {string} sid - The SID of the eSIM Profile resource to fetch
   *
   * @returns {Twilio.Supersim.V1.EsimProfileContext}
   */
  /* jshint ignore:end */
  EsimProfileListInstance.get = function get(sid) {
    return new EsimProfileContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Supersim.V1.EsimProfileList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  EsimProfileListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  EsimProfileListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return EsimProfileListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the EsimProfilePage
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.EsimProfilePage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {EsimProfileSolution} solution - Path solution
 *
 * @returns EsimProfilePage
 */
/* jshint ignore:end */
EsimProfilePage = function EsimProfilePage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(EsimProfilePage.prototype, Page.prototype);
EsimProfilePage.prototype.constructor = EsimProfilePage;

/* jshint ignore:start */
/**
 * Build an instance of EsimProfileInstance
 *
 * @function getInstance
 * @memberof Twilio.Supersim.V1.EsimProfilePage#
 *
 * @param {EsimProfilePayload} payload - Payload response from the API
 *
 * @returns EsimProfileInstance
 */
/* jshint ignore:end */
EsimProfilePage.prototype.getInstance = function getInstance(payload) {
  return new EsimProfileInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.EsimProfilePage#
 *
 * @returns Object
 */
/* jshint ignore:end */
EsimProfilePage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EsimProfilePage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EsimProfileContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.EsimProfileInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid -
 *          The SID of the Account to which the eSIM Profile resource belongs
 * @property {string} iccid - The ICCID associated with the Sim resource
 * @property {string} simSid -
 *          The SID of the Sim resource that this eSIM Profile controls
 * @property {esim_profile.status} status - The status of the eSIM Profile
 * @property {string} eid - Identifier of the eUICC that can claim the eSIM Profile
 * @property {string} smdpPlusAddress -
 *          Address of the SM-DP+ server from which the Profile will be downloaded
 * @property {string} errorCode -
 *          Code indicating the failure if the download of the SIM Profile failed and the eSIM Profile is in `failed` state
 * @property {string} errorMessage -
 *          Error message describing the failure if the download of the SIM Profile failed and the eSIM Profile is in `failed` state
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the eSIM Profile resource
 *
 * @param {V1} version - Version of the resource
 * @param {EsimProfilePayload} payload - The instance payload
 * @param {sid_like} sid - The SID of the eSIM Profile resource to fetch
 */
/* jshint ignore:end */
EsimProfileInstance = function EsimProfileInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.iccid = payload.iccid; // jshint ignore:line
  this.simSid = payload.sim_sid; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.eid = payload.eid; // jshint ignore:line
  this.smdpPlusAddress = payload.smdp_plus_address; // jshint ignore:line
  this.errorCode = payload.error_code; // jshint ignore:line
  this.errorMessage = payload.error_message; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(EsimProfileInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new EsimProfileContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a EsimProfileInstance
 *
 * @function fetch
 * @memberof Twilio.Supersim.V1.EsimProfileInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EsimProfileInstance
 */
/* jshint ignore:end */
EsimProfileInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.EsimProfileInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
EsimProfileInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

EsimProfileInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the EsimProfileContext
 *
 * PLEASE NOTE that this class contains beta products that are subject to change.
 * Use them with caution.
 *
 * @constructor Twilio.Supersim.V1.EsimProfileContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid_like} sid - The SID of the eSIM Profile resource to fetch
 */
/* jshint ignore:end */
EsimProfileContext = function EsimProfileContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/ESimProfiles/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a EsimProfileInstance
 *
 * @function fetch
 * @memberof Twilio.Supersim.V1.EsimProfileContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed EsimProfileInstance
 */
/* jshint ignore:end */
EsimProfileContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new EsimProfileInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Supersim.V1.EsimProfileContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
EsimProfileContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

EsimProfileContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  EsimProfileList: EsimProfileList,
  EsimProfilePage: EsimProfilePage,
  EsimProfileInstance: EsimProfileInstance,
  EsimProfileContext: EsimProfileContext
};
