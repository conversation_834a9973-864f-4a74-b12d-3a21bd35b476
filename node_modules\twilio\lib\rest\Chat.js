'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var _ = require('lodash');  /* jshint ignore:line */
var Domain = require('../base/Domain');  /* jshint ignore:line */
var V1 = require('./chat/V1');  /* jshint ignore:line */
var V2 = require('./chat/V2');  /* jshint ignore:line */
var V3 = require('./chat/V3');  /* jshint ignore:line */


/* jshint ignore:start */
/**
 * Initialize chat domain
 *
 * @constructor Twilio.Chat
 *
 * @property {Twilio.Chat.V1} v1 - v1 version
 * @property {Twilio.Chat.V2} v2 - v2 version
 * @property {Twilio.Chat.V3} v3 - v3 version
 * @property {Twilio.Chat.V2.CredentialList} credentials - credentials resource
 * @property {Twilio.Chat.V2.ServiceList} services - services resource
 * @property {Twilio.Chat.V3.ChannelList} channels - channels resource
 *
 * @param {Twilio} twilio - The twilio client
 */
/* jshint ignore:end */
function Chat(twilio) {
  Domain.prototype.constructor.call(this, twilio, 'https://chat.twilio.com');

  // Versions
  this._v1 = undefined;
  this._v2 = undefined;
  this._v3 = undefined;
}

_.extend(Chat.prototype, Domain.prototype);
Chat.prototype.constructor = Chat;

Object.defineProperty(Chat.prototype,
  'v1', {
    get: function() {
      this._v1 = this._v1 || new V1(this);
      return this._v1;
    }
});

Object.defineProperty(Chat.prototype,
  'v2', {
    get: function() {
      this._v2 = this._v2 || new V2(this);
      return this._v2;
    }
});

Object.defineProperty(Chat.prototype,
  'v3', {
    get: function() {
      this._v3 = this._v3 || new V3(this);
      return this._v3;
    }
});

Object.defineProperty(Chat.prototype,
  'credentials', {
    get: function() {
      return this.v2.credentials;
    }
});

Object.defineProperty(Chat.prototype,
  'services', {
    get: function() {
      return this.v2.services;
    }
});

Object.defineProperty(Chat.prototype,
  'channels', {
    get: function() {
      return this.v3.channels;
    }
});

module.exports = Chat;
