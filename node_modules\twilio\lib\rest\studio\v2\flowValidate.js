'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var FlowValidateList;
var FlowValidatePage;
var FlowValidateInstance;

/* jshint ignore:start */
/**
 * Initialize the FlowValidateList
 *
 * @constructor Twilio.Studio.V2.FlowValidateList
 *
 * @param {Twilio.Studio.V2} version - Version of the resource
 */
/* jshint ignore:end */
FlowValidateList = function FlowValidateList(version) {
  /* jshint ignore:start */
  /**
   * @function flowValidate
   * @memberof Twilio.Studio.V2#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Studio.V2.FlowValidateContext}
   */
  /* jshint ignore:end */
  function FlowValidateListInstance(sid) {
    return FlowValidateListInstance.get(sid);
  }

  FlowValidateListInstance._version = version;
  // Path Solution
  FlowValidateListInstance._solution = {};
  FlowValidateListInstance._uri = `/Flows/Validate`;
  /* jshint ignore:start */
  /**
   * update a FlowValidateInstance
   *
   * @function update
   * @memberof Twilio.Studio.V2.FlowValidateList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.friendlyName -
   *          The string that you assigned to describe the Flow
   * @param {flow_validate.status} opts.status - The status of the Flow
   * @param {object} opts.definition - JSON representation of flow definition
   * @param {string} [opts.commitMessage] -
   *          Description of change made in the revision
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed FlowValidateInstance
   */
  /* jshint ignore:end */
  FlowValidateListInstance.update = function update(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['friendlyName'])) {
      throw new Error('Required parameter "opts[\'friendlyName\']" missing.');
    }
    if (_.isUndefined(opts['status'])) {
      throw new Error('Required parameter "opts[\'status\']" missing.');
    }
    if (_.isUndefined(opts['definition'])) {
      throw new Error('Required parameter "opts[\'definition\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'FriendlyName': _.get(opts, 'friendlyName'),
      'Status': _.get(opts, 'status'),
      'Definition': serialize.object(_.get(opts, 'definition')),
      'CommitMessage': _.get(opts, 'commitMessage')
    });

    var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FlowValidateInstance(this._version, payload));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Studio.V2.FlowValidateList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FlowValidateListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FlowValidateListInstance[util.inspect.custom] = function inspect(depth, options)
      {
    return util.inspect(this.toJSON(), options);
  };

  return FlowValidateListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FlowValidatePage
 *
 * @constructor Twilio.Studio.V2.FlowValidatePage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FlowValidateSolution} solution - Path solution
 *
 * @returns FlowValidatePage
 */
/* jshint ignore:end */
FlowValidatePage = function FlowValidatePage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FlowValidatePage.prototype, Page.prototype);
FlowValidatePage.prototype.constructor = FlowValidatePage;

/* jshint ignore:start */
/**
 * Build an instance of FlowValidateInstance
 *
 * @function getInstance
 * @memberof Twilio.Studio.V2.FlowValidatePage#
 *
 * @param {FlowValidatePayload} payload - Payload response from the API
 *
 * @returns FlowValidateInstance
 */
/* jshint ignore:end */
FlowValidatePage.prototype.getInstance = function getInstance(payload) {
  return new FlowValidateInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V2.FlowValidatePage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FlowValidatePage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FlowValidatePage.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FlowValidateContext
 *
 * @constructor Twilio.Studio.V2.FlowValidateInstance
 *
 * @property {boolean} valid - Boolean if the flow definition is valid
 *
 * @param {V2} version - Version of the resource
 * @param {FlowValidatePayload} payload - The instance payload
 */
/* jshint ignore:end */
FlowValidateInstance = function FlowValidateInstance(version, payload) {
  this._version = version;

  // Marshaled Properties
  this.valid = payload.valid; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {};
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V2.FlowValidateInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FlowValidateInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FlowValidateInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FlowValidateList: FlowValidateList,
  FlowValidatePage: FlowValidatePage,
  FlowValidateInstance: FlowValidateInstance
};
