'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var ExecutionContextList = require(
    './execution/executionContext').ExecutionContextList;
var ExecutionStepList = require('./execution/executionStep').ExecutionStepList;
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var ExecutionList;
var ExecutionPage;
var ExecutionInstance;
var ExecutionContext;

/* jshint ignore:start */
/**
 * Initialize the ExecutionList
 *
 * @constructor Twilio.Studio.V2.FlowContext.ExecutionList
 *
 * @param {Twilio.Studio.V2} version - Version of the resource
 * @param {string} flowSid - The SID of the Flow
 */
/* jshint ignore:end */
ExecutionList = function ExecutionList(version, flowSid) {
  /* jshint ignore:start */
  /**
   * @function executions
   * @memberof Twilio.Studio.V2.FlowContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Studio.V2.FlowContext.ExecutionContext}
   */
  /* jshint ignore:end */
  function ExecutionListInstance(sid) {
    return ExecutionListInstance.get(sid);
  }

  ExecutionListInstance._version = version;
  // Path Solution
  ExecutionListInstance._solution = {flowSid: flowSid};
  ExecutionListInstance._uri = `/Flows/${flowSid}/Executions`;
  /* jshint ignore:start */
  /**
   * Streams ExecutionInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.dateCreatedFrom] -
   *          Only show Executions that started on or after this ISO 8601 date-time
   * @param {Date} [opts.dateCreatedTo] -
   *          Only show Executions that started before this ISO 8601 date-time
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  ExecutionListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists ExecutionInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.dateCreatedFrom] -
   *          Only show Executions that started on or after this ISO 8601 date-time
   * @param {Date} [opts.dateCreatedTo] -
   *          Only show Executions that started before this ISO 8601 date-time
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ExecutionListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of ExecutionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {object} [opts] - Options for request
   * @param {Date} [opts.dateCreatedFrom] -
   *          Only show Executions that started on or after this ISO 8601 date-time
   * @param {Date} [opts.dateCreatedTo] -
   *          Only show Executions that started before this ISO 8601 date-time
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ExecutionListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'DateCreatedFrom': serialize.iso8601DateTime(_.get(opts, 'dateCreatedFrom')),
      'DateCreatedTo': serialize.iso8601DateTime(_.get(opts, 'dateCreatedTo')),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ExecutionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of ExecutionInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ExecutionListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new ExecutionPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * create a ExecutionInstance
   *
   * @function create
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.to -
   *          The Contact phone number to start a Studio Flow Execution
   * @param {string} opts.from -
   *          The Twilio phone number or Messaging Service SID to send messages or initiate calls from during the Flow Execution
   * @param {object} [opts.parameters] -
   *          JSON data that will be added to the Flow's context
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed ExecutionInstance
   */
  /* jshint ignore:end */
  ExecutionListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['to'])) {
      throw new Error('Required parameter "opts[\'to\']" missing.');
    }
    if (_.isUndefined(opts['from'])) {
      throw new Error('Required parameter "opts[\'from\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'To': _.get(opts, 'to'),
      'From': _.get(opts, 'from'),
      'Parameters': serialize.object(_.get(opts, 'parameters'))
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ExecutionInstance(
        this._version,
        payload,
        this._solution.flowSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a execution
   *
   * @function get
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @param {string} sid - The SID of the Execution resource to fetch
   *
   * @returns {Twilio.Studio.V2.FlowContext.ExecutionContext}
   */
  /* jshint ignore:end */
  ExecutionListInstance.get = function get(sid) {
    return new ExecutionContext(this._version, this._solution.flowSid, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Studio.V2.FlowContext.ExecutionList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ExecutionListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ExecutionListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return ExecutionListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionPage
 *
 * @constructor Twilio.Studio.V2.FlowContext.ExecutionPage
 *
 * @param {V2} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ExecutionSolution} solution - Path solution
 *
 * @returns ExecutionPage
 */
/* jshint ignore:end */
ExecutionPage = function ExecutionPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ExecutionPage.prototype, Page.prototype);
ExecutionPage.prototype.constructor = ExecutionPage;

/* jshint ignore:start */
/**
 * Build an instance of ExecutionInstance
 *
 * @function getInstance
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionPage#
 *
 * @param {ExecutionPayload} payload - Payload response from the API
 *
 * @returns ExecutionInstance
 */
/* jshint ignore:end */
ExecutionPage.prototype.getInstance = function getInstance(payload) {
  return new ExecutionInstance(this._version, payload, this._solution.flowSid);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExecutionPage.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionContext
 *
 * @constructor Twilio.Studio.V2.FlowContext.ExecutionInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} flowSid - The SID of the Flow
 * @property {string} contactChannelAddress -
 *          The phone number, SIP address or Client identifier that triggered the Execution
 * @property {object} context - The current state of the flow
 * @property {execution.status} status - The status of the Execution
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} url - The absolute URL of the resource
 * @property {string} links - Nested resource URLs
 *
 * @param {V2} version - Version of the resource
 * @param {ExecutionPayload} payload - The instance payload
 * @param {sid} flowSid - The SID of the Flow
 * @param {sid} sid - The SID of the Execution resource to fetch
 */
/* jshint ignore:end */
ExecutionInstance = function ExecutionInstance(version, payload, flowSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.flowSid = payload.flow_sid; // jshint ignore:line
  this.contactChannelAddress = payload.contact_channel_address; // jshint ignore:line
  this.context = payload.context; // jshint ignore:line
  this.status = payload.status; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line
  this.links = payload.links; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {flowSid: flowSid, sid: sid || this.sid, };
};

Object.defineProperty(ExecutionInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ExecutionContext(this._version, this._solution.flowSid, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a ExecutionInstance
 *
 * @function fetch
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a ExecutionInstance
 *
 * @function remove
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * update a ExecutionInstance
 *
 * @function update
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @param {object} opts - Options for request
 * @param {execution.status} opts.status - The status of the Execution
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * Access the steps
 *
 * @function steps
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @returns {Twilio.Studio.V2.FlowContext.ExecutionContext.ExecutionStepList}
 */
/* jshint ignore:end */
ExecutionInstance.prototype.steps = function steps() {
  return this._proxy.steps;
};

/* jshint ignore:start */
/**
 * Access the executionContext
 *
 * @function executionContext
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @returns {Twilio.Studio.V2.FlowContext.ExecutionContext.ExecutionContextList}
 */
/* jshint ignore:end */
ExecutionInstance.prototype.executionContext = function executionContext() {
  return this._proxy.executionContext;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExecutionInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionContext
 *
 * @constructor Twilio.Studio.V2.FlowContext.ExecutionContext
 *
 * @property {Twilio.Studio.V2.FlowContext.ExecutionContext.ExecutionStepList} steps -
 *          steps resource
 * @property {Twilio.Studio.V2.FlowContext.ExecutionContext.ExecutionContextList} executionContext -
 *          executionContext resource
 *
 * @param {V2} version - Version of the resource
 * @param {sid} flowSid - The SID of the Flow
 * @param {sid} sid - The SID of the Execution resource to fetch
 */
/* jshint ignore:end */
ExecutionContext = function ExecutionContext(version, flowSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {flowSid: flowSid, sid: sid, };
  this._uri = `/Flows/${flowSid}/Executions/${sid}`;

  // Dependents
  this._steps = undefined;
  this._executionContext = undefined;
};

/* jshint ignore:start */
/**
 * fetch a ExecutionInstance
 *
 * @function fetch
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new ExecutionInstance(
      this._version,
      payload,
      this._solution.flowSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a ExecutionInstance
 *
 * @function remove
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a ExecutionInstance
 *
 * @function update
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionContext#
 *
 * @param {object} opts - Options for request
 * @param {execution.status} opts.status - The status of the Execution
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionInstance
 */
/* jshint ignore:end */
ExecutionContext.prototype.update = function update(opts, callback) {
  if (_.isUndefined(opts)) {
    throw new Error('Required parameter "opts" missing.');
  }
  if (_.isUndefined(opts['status'])) {
    throw new Error('Required parameter "opts[\'status\']" missing.');
  }

  var deferred = Q.defer();
  var data = values.of({'Status': _.get(opts, 'status')});

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new ExecutionInstance(
      this._version,
      payload,
      this._solution.flowSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

Object.defineProperty(ExecutionContext.prototype,
  'steps', {
    get: function() {
      if (!this._steps) {
        this._steps = new ExecutionStepList(this._version, this._solution.flowSid, this._solution.sid);
      }
      return this._steps;
    }
});

Object.defineProperty(ExecutionContext.prototype,
  'executionContext', {
    get: function() {
      if (!this._executionContext) {
        this._executionContext = new ExecutionContextList(
          this._version,
          this._solution.flowSid,
          this._solution.sid
        );
      }
      return this._executionContext;
    }
});

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V2.FlowContext.ExecutionContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ExecutionContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ExecutionList: ExecutionList,
  ExecutionPage: ExecutionPage,
  ExecutionInstance: ExecutionInstance,
  ExecutionContext: ExecutionContext
};
