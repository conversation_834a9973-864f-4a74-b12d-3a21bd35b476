'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var FlexFlowList;
var FlexFlowPage;
var FlexFlowInstance;
var FlexFlowContext;

/* jshint ignore:start */
/**
 * Initialize the FlexFlowList
 *
 * @constructor Twilio.FlexApi.V1.FlexFlowList
 *
 * @param {Twilio.FlexApi.V1} version - Version of the resource
 */
/* jshint ignore:end */
FlexFlowList = function FlexFlowList(version) {
  /* jshint ignore:start */
  /**
   * @function flexFlow
   * @memberof Twilio.FlexApi.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.FlexApi.V1.FlexFlowContext}
   */
  /* jshint ignore:end */
  function FlexFlowListInstance(sid) {
    return FlexFlowListInstance.get(sid);
  }

  FlexFlowListInstance._version = version;
  // Path Solution
  FlexFlowListInstance._solution = {};
  FlexFlowListInstance._uri = `/FlexFlows`;
  /* jshint ignore:start */
  /**
   * Streams FlexFlowInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.friendlyName] -
   *          The `friendly_name` of the Flex Flow resources to read
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  FlexFlowListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists FlexFlowInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.friendlyName] -
   *          The `friendly_name` of the Flex Flow resources to read
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FlexFlowListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of FlexFlowInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.friendlyName] -
   *          The `friendly_name` of the Flex Flow resources to read
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FlexFlowListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'FriendlyName': _.get(opts, 'friendlyName'),
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FlexFlowPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of FlexFlowInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  FlexFlowListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new FlexFlowPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * create a FlexFlowInstance
   *
   * @function create
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.friendlyName - A string to describe the resource
   * @param {string} opts.chatServiceSid - The SID of the chat service
   * @param {flex_flow.channel_type} opts.channelType - The channel type
   * @param {string} [opts.contactIdentity] - The channel contact's Identity
   * @param {boolean} [opts.enabled] - Whether the new Flex Flow is enabled
   * @param {flex_flow.integration_type} [opts.integrationType] -
   *          The software that will handle inbound messages.
   * @param {string} [opts.integration.flowSid] - The SID of the Studio Flow
   * @param {string} [opts.integration.url] - The External Webhook URL
   * @param {string} [opts.integration.workspaceSid] -
   *          The Workspace SID for a new Task
   * @param {string} [opts.integration.workflowSid] - The Workflow SID for a new Task
   * @param {string} [opts.integration.channel] - The Task Channel for a new Task
   * @param {number} [opts.integration.timeout] -
   *          The Task timeout in seconds for a new Task
   * @param {number} [opts.integration.priority] - The Task priority of a new Task
   * @param {boolean} [opts.integration.creationOnMessage] -
   *          Whether to create a Task when the first message arrives
   * @param {boolean} [opts.longLived] -
   *          Reuse this chat channel for future interactions with a contact
   * @param {boolean} [opts.janitorEnabled] -
   *          Remove active Proxy sessions if the corresponding Task is deleted
   * @param {number} [opts.integration.retryCount] -
   *          The number of times to retry the Studio Flow or webhook in case of failure
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed FlexFlowInstance
   */
  /* jshint ignore:end */
  FlexFlowListInstance.create = function create(opts, callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['friendlyName'])) {
      throw new Error('Required parameter "opts[\'friendlyName\']" missing.');
    }
    if (_.isUndefined(opts['chatServiceSid'])) {
      throw new Error('Required parameter "opts[\'chatServiceSid\']" missing.');
    }
    if (_.isUndefined(opts['channelType'])) {
      throw new Error('Required parameter "opts[\'channelType\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({
      'FriendlyName': _.get(opts, 'friendlyName'),
      'ChatServiceSid': _.get(opts, 'chatServiceSid'),
      'ChannelType': _.get(opts, 'channelType'),
      'ContactIdentity': _.get(opts, 'contactIdentity'),
      'Enabled': serialize.bool(_.get(opts, 'enabled')),
      'IntegrationType': _.get(opts, 'integrationType'),
      'Integration.FlowSid': _.get(opts, 'integration.flowSid'),
      'Integration.Url': _.get(opts, 'integration.url'),
      'Integration.WorkspaceSid': _.get(opts, 'integration.workspaceSid'),
      'Integration.WorkflowSid': _.get(opts, 'integration.workflowSid'),
      'Integration.Channel': _.get(opts, 'integration.channel'),
      'Integration.Timeout': _.get(opts, 'integration.timeout'),
      'Integration.Priority': _.get(opts, 'integration.priority'),
      'Integration.CreationOnMessage': serialize.bool(_.get(opts, 'integration.creationOnMessage')),
      'LongLived': serialize.bool(_.get(opts, 'longLived')),
      'JanitorEnabled': serialize.bool(_.get(opts, 'janitorEnabled')),
      'Integration.RetryCount': _.get(opts, 'integration.retryCount')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new FlexFlowInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a flex_flow
   *
   * @function get
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @param {string} sid - The SID that identifies the resource to fetch
   *
   * @returns {Twilio.FlexApi.V1.FlexFlowContext}
   */
  /* jshint ignore:end */
  FlexFlowListInstance.get = function get(sid) {
    return new FlexFlowContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.FlexApi.V1.FlexFlowList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  FlexFlowListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  FlexFlowListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return FlexFlowListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the FlexFlowPage
 *
 * @constructor Twilio.FlexApi.V1.FlexFlowPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {FlexFlowSolution} solution - Path solution
 *
 * @returns FlexFlowPage
 */
/* jshint ignore:end */
FlexFlowPage = function FlexFlowPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(FlexFlowPage.prototype, Page.prototype);
FlexFlowPage.prototype.constructor = FlexFlowPage;

/* jshint ignore:start */
/**
 * Build an instance of FlexFlowInstance
 *
 * @function getInstance
 * @memberof Twilio.FlexApi.V1.FlexFlowPage#
 *
 * @param {FlexFlowPayload} payload - Payload response from the API
 *
 * @returns FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowPage.prototype.getInstance = function getInstance(payload) {
  return new FlexFlowInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.FlexFlowPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
FlexFlowPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FlexFlowPage.prototype[util.inspect.custom] = function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FlexFlowContext
 *
 * @constructor Twilio.FlexApi.V1.FlexFlowInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {Date} dateUpdated -
 *          The ISO 8601 date and time in GMT when the resource was last updated
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {string} chatServiceSid - The SID of the chat service
 * @property {flex_flow.channel_type} channelType - The channel type
 * @property {string} contactIdentity - The channel contact's Identity
 * @property {boolean} enabled - Whether the Flex Flow is enabled
 * @property {flex_flow.integration_type} integrationType -
 *          The software that will handle inbound messages.
 * @property {object} integration -
 *          An object that contains specific parameters for the integration
 * @property {boolean} longLived -
 *          Re-use this chat channel for future interactions with a contact
 * @property {boolean} janitorEnabled -
 *          Remove active Proxy sessions if the corresponding Task is deleted.
 * @property {string} url - The absolute URL of the Flex Flow resource
 *
 * @param {V1} version - Version of the resource
 * @param {FlexFlowPayload} payload - The instance payload
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
FlexFlowInstance = function FlexFlowInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.chatServiceSid = payload.chat_service_sid; // jshint ignore:line
  this.channelType = payload.channel_type; // jshint ignore:line
  this.contactIdentity = payload.contact_identity; // jshint ignore:line
  this.enabled = payload.enabled; // jshint ignore:line
  this.integrationType = payload.integration_type; // jshint ignore:line
  this.integration = payload.integration; // jshint ignore:line
  this.longLived = payload.long_lived; // jshint ignore:line
  this.janitorEnabled = payload.janitor_enabled; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(FlexFlowInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new FlexFlowContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a FlexFlowInstance
 *
 * @function fetch
 * @memberof Twilio.FlexApi.V1.FlexFlowInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a FlexFlowInstance
 *
 * @function update
 * @memberof Twilio.FlexApi.V1.FlexFlowInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.chatServiceSid] - The SID of the chat service
 * @param {flex_flow.channel_type} [opts.channelType] - The channel type
 * @param {string} [opts.contactIdentity] - The channel contact's Identity
 * @param {boolean} [opts.enabled] - Whether the new Flex Flow is enabled
 * @param {flex_flow.integration_type} [opts.integrationType] -
 *          The software that will handle inbound messages.
 * @param {string} [opts.integration.flowSid] - The SID of the Studio Flow
 * @param {string} [opts.integration.url] - The External Webhook URL
 * @param {string} [opts.integration.workspaceSid] -
 *          The Workspace SID for a new Task
 * @param {string} [opts.integration.workflowSid] - The Workflow SID for a new Task
 * @param {string} [opts.integration.channel] - The Task Channel for a new Task
 * @param {number} [opts.integration.timeout] -
 *          The Task timeout in seconds for a new Task
 * @param {number} [opts.integration.priority] - The Task priority of a new Task
 * @param {boolean} [opts.integration.creationOnMessage] -
 *          Whether to create a Task when the first message arrives
 * @param {boolean} [opts.longLived] -
 *          Reuse this chat channel for future interactions with a contact
 * @param {boolean} [opts.janitorEnabled] -
 *          Remove active Proxy sessions if the corresponding Task is deleted
 * @param {number} [opts.integration.retryCount] -
 *          The number of times to retry the Studio Flow or webhook in case of failure
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * remove a FlexFlowInstance
 *
 * @function remove
 * @memberof Twilio.FlexApi.V1.FlexFlowInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.FlexFlowInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
FlexFlowInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

FlexFlowInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the FlexFlowContext
 *
 * @constructor Twilio.FlexApi.V1.FlexFlowContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The SID that identifies the resource to fetch
 */
/* jshint ignore:end */
FlexFlowContext = function FlexFlowContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/FlexFlows/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a FlexFlowInstance
 *
 * @function fetch
 * @memberof Twilio.FlexApi.V1.FlexFlowContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new FlexFlowInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a FlexFlowInstance
 *
 * @function update
 * @memberof Twilio.FlexApi.V1.FlexFlowContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.chatServiceSid] - The SID of the chat service
 * @param {flex_flow.channel_type} [opts.channelType] - The channel type
 * @param {string} [opts.contactIdentity] - The channel contact's Identity
 * @param {boolean} [opts.enabled] - Whether the new Flex Flow is enabled
 * @param {flex_flow.integration_type} [opts.integrationType] -
 *          The software that will handle inbound messages.
 * @param {string} [opts.integration.flowSid] - The SID of the Studio Flow
 * @param {string} [opts.integration.url] - The External Webhook URL
 * @param {string} [opts.integration.workspaceSid] -
 *          The Workspace SID for a new Task
 * @param {string} [opts.integration.workflowSid] - The Workflow SID for a new Task
 * @param {string} [opts.integration.channel] - The Task Channel for a new Task
 * @param {number} [opts.integration.timeout] -
 *          The Task timeout in seconds for a new Task
 * @param {number} [opts.integration.priority] - The Task priority of a new Task
 * @param {boolean} [opts.integration.creationOnMessage] -
 *          Whether to create a Task when the first message arrives
 * @param {boolean} [opts.longLived] -
 *          Reuse this chat channel for future interactions with a contact
 * @param {boolean} [opts.janitorEnabled] -
 *          Remove active Proxy sessions if the corresponding Task is deleted
 * @param {number} [opts.integration.retryCount] -
 *          The number of times to retry the Studio Flow or webhook in case of failure
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'FriendlyName': _.get(opts, 'friendlyName'),
    'ChatServiceSid': _.get(opts, 'chatServiceSid'),
    'ChannelType': _.get(opts, 'channelType'),
    'ContactIdentity': _.get(opts, 'contactIdentity'),
    'Enabled': serialize.bool(_.get(opts, 'enabled')),
    'IntegrationType': _.get(opts, 'integrationType'),
    'Integration.FlowSid': _.get(opts, 'integration.flowSid'),
    'Integration.Url': _.get(opts, 'integration.url'),
    'Integration.WorkspaceSid': _.get(opts, 'integration.workspaceSid'),
    'Integration.WorkflowSid': _.get(opts, 'integration.workflowSid'),
    'Integration.Channel': _.get(opts, 'integration.channel'),
    'Integration.Timeout': _.get(opts, 'integration.timeout'),
    'Integration.Priority': _.get(opts, 'integration.priority'),
    'Integration.CreationOnMessage': serialize.bool(_.get(opts, 'integration.creationOnMessage')),
    'LongLived': serialize.bool(_.get(opts, 'longLived')),
    'JanitorEnabled': serialize.bool(_.get(opts, 'janitorEnabled')),
    'Integration.RetryCount': _.get(opts, 'integration.retryCount')
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new FlexFlowInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a FlexFlowInstance
 *
 * @function remove
 * @memberof Twilio.FlexApi.V1.FlexFlowContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed FlexFlowInstance
 */
/* jshint ignore:end */
FlexFlowContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.FlexApi.V1.FlexFlowContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
FlexFlowContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

FlexFlowContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  FlexFlowList: FlexFlowList,
  FlexFlowPage: FlexFlowPage,
  FlexFlowInstance: FlexFlowInstance,
  FlexFlowContext: FlexFlowContext
};
