'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../../base/deserialize');  /* jshint ignore:line */
var values = require('../../../../base/values');  /* jshint ignore:line */

var CustomerProfilesEntityAssignmentsList;
var CustomerProfilesEntityAssignmentsPage;
var CustomerProfilesEntityAssignmentsInstance;
var CustomerProfilesEntityAssignmentsContext;

/* jshint ignore:start */
/**
 * Initialize the CustomerProfilesEntityAssignmentsList
 *
 * @constructor Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList
 *
 * @param {Twilio.Trusthub.V1} version - Version of the resource
 * @param {string} customerProfileSid -
 *          The unique string that identifies the CustomerProfile resource.
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsList = function
    CustomerProfilesEntityAssignmentsList(version, customerProfileSid) {
  /* jshint ignore:start */
  /**
   * @function customerProfilesEntityAssignments
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext}
   */
  /* jshint ignore:end */
  function CustomerProfilesEntityAssignmentsListInstance(sid) {
    return CustomerProfilesEntityAssignmentsListInstance.get(sid);
  }

  CustomerProfilesEntityAssignmentsListInstance._version = version;
  // Path Solution
  CustomerProfilesEntityAssignmentsListInstance._solution = {customerProfileSid: customerProfileSid};
  CustomerProfilesEntityAssignmentsListInstance._uri = `/CustomerProfiles/${customerProfileSid}/EntityAssignments`;
  /* jshint ignore:start */
  /**
   * create a CustomerProfilesEntityAssignmentsInstance
   *
   * @function create
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {object} opts - Options for request
   * @param {string} opts.objectSid - The sid of an object bag
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed CustomerProfilesEntityAssignmentsInstance
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.create = function create(opts,
      callback) {
    if (_.isUndefined(opts)) {
      throw new Error('Required parameter "opts" missing.');
    }
    if (_.isUndefined(opts['objectSid'])) {
      throw new Error('Required parameter "opts[\'objectSid\']" missing.');
    }

    var deferred = Q.defer();
    var data = values.of({'ObjectSid': _.get(opts, 'objectSid')});

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new CustomerProfilesEntityAssignmentsInstance(
        this._version,
        payload,
        this._solution.customerProfileSid,
        this._solution.sid
      ));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams CustomerProfilesEntityAssignmentsInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.each = function each(opts,
      callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists CustomerProfilesEntityAssignmentsInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.list = function list(opts,
      callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of CustomerProfilesEntityAssignmentsInstance records from
   * the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.page = function page(opts,
      callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new CustomerProfilesEntityAssignmentsPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of CustomerProfilesEntityAssignmentsInstance
   * records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.getPage = function
      getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new CustomerProfilesEntityAssignmentsPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a customer_profiles_entity_assignments
   *
   * @function get
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext}
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.get = function get(sid) {
    return new CustomerProfilesEntityAssignmentsContext(
      this._version,
      this._solution.customerProfileSid,
      sid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  CustomerProfilesEntityAssignmentsListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  CustomerProfilesEntityAssignmentsListInstance[util.inspect.custom] = function
      inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return CustomerProfilesEntityAssignmentsListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the CustomerProfilesEntityAssignmentsPage
 *
 * @constructor Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {CustomerProfilesEntityAssignmentsSolution} solution - Path solution
 *
 * @returns CustomerProfilesEntityAssignmentsPage
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsPage = function
    CustomerProfilesEntityAssignmentsPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(CustomerProfilesEntityAssignmentsPage.prototype, Page.prototype);
CustomerProfilesEntityAssignmentsPage.prototype.constructor = CustomerProfilesEntityAssignmentsPage;

/* jshint ignore:start */
/**
 * Build an instance of CustomerProfilesEntityAssignmentsInstance
 *
 * @function getInstance
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsPage#
 *
 * @param {CustomerProfilesEntityAssignmentsPayload} payload -
 *          Payload response from the API
 *
 * @returns CustomerProfilesEntityAssignmentsInstance
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsPage.prototype.getInstance = function
    getInstance(payload) {
  return new CustomerProfilesEntityAssignmentsInstance(
    this._version,
    payload,
    this._solution.customerProfileSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

CustomerProfilesEntityAssignmentsPage.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the CustomerProfilesEntityAssignmentsContext
 *
 * @constructor Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsInstance
 *
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} customerProfileSid -
 *          The unique string that identifies the CustomerProfile resource.
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} objectSid - The sid of an object bag
 * @property {Date} dateCreated -
 *          The ISO 8601 date and time in GMT when the resource was created
 * @property {string} url - The absolute URL of the Identity resource
 *
 * @param {V1} version - Version of the resource
 * @param {CustomerProfilesEntityAssignmentsPayload} payload - The instance payload
 * @param {sid} customerProfileSid -
 *          The unique string that identifies the CustomerProfile resource.
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsInstance = function
    CustomerProfilesEntityAssignmentsInstance(version, payload,
    customerProfileSid, sid) {
  this._version = version;

  // Marshaled Properties
  this.sid = payload.sid; // jshint ignore:line
  this.customerProfileSid = payload.customer_profile_sid; // jshint ignore:line
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.objectSid = payload.object_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {customerProfileSid: customerProfileSid, sid: sid || this.sid, };
};

Object.defineProperty(CustomerProfilesEntityAssignmentsInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new CustomerProfilesEntityAssignmentsContext(
          this._version,
          this._solution.customerProfileSid,
          this._solution.sid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a CustomerProfilesEntityAssignmentsInstance
 *
 * @function fetch
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CustomerProfilesEntityAssignmentsInstance
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsInstance.prototype.fetch = function
    fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * remove a CustomerProfilesEntityAssignmentsInstance
 *
 * @function remove
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CustomerProfilesEntityAssignmentsInstance
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsInstance.prototype.remove = function
    remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

CustomerProfilesEntityAssignmentsInstance.prototype[util.inspect.custom] =
    function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the CustomerProfilesEntityAssignmentsContext
 *
 * @constructor Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} customerProfileSid -
 *          The unique string that identifies the resource.
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsContext = function
    CustomerProfilesEntityAssignmentsContext(version, customerProfileSid, sid) {
  this._version = version;

  // Path Solution
  this._solution = {customerProfileSid: customerProfileSid, sid: sid, };
  this._uri = `/CustomerProfiles/${customerProfileSid}/EntityAssignments/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a CustomerProfilesEntityAssignmentsInstance
 *
 * @function fetch
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CustomerProfilesEntityAssignmentsInstance
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsContext.prototype.fetch = function
    fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new CustomerProfilesEntityAssignmentsInstance(
      this._version,
      payload,
      this._solution.customerProfileSid,
      this._solution.sid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a CustomerProfilesEntityAssignmentsInstance
 *
 * @function remove
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed CustomerProfilesEntityAssignmentsInstance
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsContext.prototype.remove = function
    remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Trusthub.V1.CustomerProfilesContext.CustomerProfilesEntityAssignmentsContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
CustomerProfilesEntityAssignmentsContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

CustomerProfilesEntityAssignmentsContext.prototype[util.inspect.custom] =
    function inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  CustomerProfilesEntityAssignmentsList: CustomerProfilesEntityAssignmentsList,
  CustomerProfilesEntityAssignmentsPage: CustomerProfilesEntityAssignmentsPage,
  CustomerProfilesEntityAssignmentsInstance: CustomerProfilesEntityAssignmentsInstance,
  CustomerProfilesEntityAssignmentsContext: CustomerProfilesEntityAssignmentsContext
};
