'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../base/Page');  /* jshint ignore:line */
var deserialize = require(
    '../../../base/deserialize');  /* jshint ignore:line */
var serialize = require('../../../base/serialize');  /* jshint ignore:line */
var values = require('../../../base/values');  /* jshint ignore:line */

var ByocTrunkList;
var ByocTrunkPage;
var ByocTrunkInstance;
var ByocTrunkContext;

/* jshint ignore:start */
/**
 * Initialize the ByocTrunkList
 *
 * @constructor Twilio.Voice.V1.ByocTrunkList
 *
 * @param {Twilio.Voice.V1} version - Version of the resource
 */
/* jshint ignore:end */
ByocTrunkList = function ByocTrunkList(version) {
  /* jshint ignore:start */
  /**
   * @function byocTrunks
   * @memberof Twilio.Voice.V1#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Voice.V1.ByocTrunkContext}
   */
  /* jshint ignore:end */
  function ByocTrunkListInstance(sid) {
    return ByocTrunkListInstance.get(sid);
  }

  ByocTrunkListInstance._version = version;
  // Path Solution
  ByocTrunkListInstance._solution = {};
  ByocTrunkListInstance._uri = `/ByocTrunks`;
  /* jshint ignore:start */
  /**
   * create a ByocTrunkInstance
   *
   * @function create
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.friendlyName] - A string to describe the resource
   * @param {string} [opts.voiceUrl] - The URL we should call when receiving a call
   * @param {string} [opts.voiceMethod] - The HTTP method to use with voice_url
   * @param {string} [opts.voiceFallbackUrl] -
   *          The URL we should call when an error occurs in executing TwiML
   * @param {string} [opts.voiceFallbackMethod] -
   *          The HTTP method to use with voice_fallback_url
   * @param {string} [opts.statusCallbackUrl] -
   *          The URL that we should call to pass status updates
   * @param {string} [opts.statusCallbackMethod] -
   *          The HTTP method we should use to call `status_callback_url`
   * @param {boolean} [opts.cnamLookupEnabled] -
   *          Whether Caller ID Name (CNAM) lookup is enabled for the trunk
   * @param {string} [opts.connectionPolicySid] -
   *          Origination Connection Policy (to your Carrier)
   * @param {string} [opts.fromDomainSid] -
   *          The SID of the SIP Domain that should be used in the `From` header of originating calls
   * @param {function} [callback] - Callback to handle processed record
   *
   * @returns {Promise} Resolves to processed ByocTrunkInstance
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.create = function create(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'FriendlyName': _.get(opts, 'friendlyName'),
      'VoiceUrl': _.get(opts, 'voiceUrl'),
      'VoiceMethod': _.get(opts, 'voiceMethod'),
      'VoiceFallbackUrl': _.get(opts, 'voiceFallbackUrl'),
      'VoiceFallbackMethod': _.get(opts, 'voiceFallbackMethod'),
      'StatusCallbackUrl': _.get(opts, 'statusCallbackUrl'),
      'StatusCallbackMethod': _.get(opts, 'statusCallbackMethod'),
      'CnamLookupEnabled': serialize.bool(_.get(opts, 'cnamLookupEnabled')),
      'ConnectionPolicySid': _.get(opts, 'connectionPolicySid'),
      'FromDomainSid': _.get(opts, 'fromDomainSid')
    });

    var promise = this._version.create({uri: this._uri, method: 'POST', data: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ByocTrunkInstance(this._version, payload, this._solution.sid));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Streams ByocTrunkInstance records from the API.
   *
   * This operation lazily loads records as efficiently as possible until the limit
   * is reached.
   *
   * The results are passed into the callback function, so this operation is memory
   * efficient.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function each
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         each() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no pageSize is defined but a limit is defined,
   *         each() will attempt to read the limit with the most efficient
   *         page size, i.e. min(limit, 1000)
   * @param {Function} [opts.callback] -
   *         Function to process each record. If this and a positional
   *         callback are passed, this one will be used
   * @param {Function} [opts.done] -
   *          Function to be called upon completion of streaming
   * @param {Function} [callback] - Function to process each record
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.each = function each(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    if (opts.callback) {
      callback = opts.callback;
    }
    if (_.isUndefined(callback)) {
      throw new Error('Callback function must be provided');
    }

    var done = false;
    var currentPage = 1;
    var currentResource = 0;
    var limits = this._version.readLimits({
      limit: opts.limit,
      pageSize: opts.pageSize
    });

    function onComplete(error) {
      done = true;
      if (_.isFunction(opts.done)) {
        opts.done(error);
      }
    }

    function fetchNextPage(fn) {
      var promise = fn();
      if (_.isUndefined(promise)) {
        onComplete();
        return;
      }

      promise.then(function(page) {
        _.each(page.instances, function(instance) {
          if (done || (!_.isUndefined(opts.limit) && currentResource >= opts.limit)) {
            done = true;
            return false;
          }

          currentResource++;
          callback(instance, onComplete);
        });

        if (!done) {
          currentPage++;
          fetchNextPage(_.bind(page.nextPage, page));
        } else {
          onComplete();
        }
      });

      promise.catch(onComplete);
    }

    fetchNextPage(_.bind(this.page, this, _.merge(opts, limits)));
  };

  /* jshint ignore:start */
  /**
   * Lists ByocTrunkInstance records from the API as a list.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function list
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {object} [opts] - Options for request
   * @param {number} [opts.limit] -
   *         Upper limit for the number of records to return.
   *         list() guarantees never to return more than limit.
   *         Default is no limit
   * @param {number} [opts.pageSize] -
   *         Number of records to fetch per request,
   *         when not set will use the default value of 50 records.
   *         If no page_size is defined but a limit is defined,
   *         list() will attempt to read the limit with the most
   *         efficient page size, i.e. min(limit, 1000)
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.list = function list(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};
    var deferred = Q.defer();
    var allResources = [];
    opts.callback = function(resource, done) {
      allResources.push(resource);

      if (!_.isUndefined(opts.limit) && allResources.length === opts.limit) {
        done();
      }
    };

    opts.done = function(error) {
      if (_.isUndefined(error)) {
        deferred.resolve(allResources);
      } else {
        deferred.reject(error);
      }
    };

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    this.each(opts);
    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single page of ByocTrunkInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function page
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {object} [opts] - Options for request
   * @param {string} [opts.pageToken] - PageToken provided by the API
   * @param {number} [opts.pageNumber] -
   *          Page Number, this value is simply for client state
   * @param {number} [opts.pageSize] - Number of records to return, defaults to 50
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.page = function page(opts, callback) {
    if (_.isFunction(opts)) {
      callback = opts;
      opts = {};
    }
    opts = opts || {};

    var deferred = Q.defer();
    var data = values.of({
      'PageToken': opts.pageToken,
      'Page': opts.pageNumber,
      'PageSize': opts.pageSize
    });

    var promise = this._version.page({uri: this._uri, method: 'GET', params: data});

    promise = promise.then(function(payload) {
      deferred.resolve(new ByocTrunkPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Retrieve a single target page of ByocTrunkInstance records from the API.
   *
   * The request is executed immediately.
   *
   * If a function is passed as the first argument, it will be used as the callback
   * function.
   *
   * @function getPage
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {string} [targetUrl] - API-generated URL for the requested results page
   * @param {function} [callback] - Callback to handle list of records
   *
   * @returns {Promise} Resolves to a list of records
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.getPage = function getPage(targetUrl, callback) {
    var deferred = Q.defer();

    var promise = this._version._domain.twilio.request({method: 'GET', uri: targetUrl});

    promise = promise.then(function(payload) {
      deferred.resolve(new ByocTrunkPage(this._version, payload, this._solution));
    }.bind(this));

    promise.catch(function(error) {
      deferred.reject(error);
    });

    if (_.isFunction(callback)) {
      deferred.promise.nodeify(callback);
    }

    return deferred.promise;
  };

  /* jshint ignore:start */
  /**
   * Constructs a byoc_trunk
   *
   * @function get
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @param {string} sid - The unique string that identifies the resource
   *
   * @returns {Twilio.Voice.V1.ByocTrunkContext}
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.get = function get(sid) {
    return new ByocTrunkContext(this._version, sid);
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Voice.V1.ByocTrunkList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ByocTrunkListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ByocTrunkListInstance[util.inspect.custom] = function inspect(depth, options) {
    return util.inspect(this.toJSON(), options);
  };

  return ByocTrunkListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ByocTrunkPage
 *
 * @constructor Twilio.Voice.V1.ByocTrunkPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ByocTrunkSolution} solution - Path solution
 *
 * @returns ByocTrunkPage
 */
/* jshint ignore:end */
ByocTrunkPage = function ByocTrunkPage(version, response, solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ByocTrunkPage.prototype, Page.prototype);
ByocTrunkPage.prototype.constructor = ByocTrunkPage;

/* jshint ignore:start */
/**
 * Build an instance of ByocTrunkInstance
 *
 * @function getInstance
 * @memberof Twilio.Voice.V1.ByocTrunkPage#
 *
 * @param {ByocTrunkPayload} payload - Payload response from the API
 *
 * @returns ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkPage.prototype.getInstance = function getInstance(payload) {
  return new ByocTrunkInstance(this._version, payload);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ByocTrunkPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ByocTrunkPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ByocTrunkPage.prototype[util.inspect.custom] = function inspect(depth, options)
    {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ByocTrunkContext
 *
 * @constructor Twilio.Voice.V1.ByocTrunkInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {string} sid - The unique string that identifies the resource
 * @property {string} friendlyName -
 *          The string that you assigned to describe the resource
 * @property {string} voiceUrl - The URL we call when receiving a call
 * @property {string} voiceMethod - The HTTP method to use with voice_url
 * @property {string} voiceFallbackUrl -
 *          The URL we call when an error occurs while executing TwiML
 * @property {string} voiceFallbackMethod -
 *          The HTTP method used with voice_fallback_url
 * @property {string} statusCallbackUrl - The URL that we call with status updates
 * @property {string} statusCallbackMethod -
 *          The HTTP method we use to call status_callback_url
 * @property {boolean} cnamLookupEnabled -
 *          Whether Caller ID Name (CNAM) lookup is enabled for the trunk
 * @property {string} connectionPolicySid -
 *          Origination Connection Policy (to your Carrier)
 * @property {string} fromDomainSid -
 *          The SID of the SIP Domain that should be used in the `From` header of originating calls
 * @property {Date} dateCreated -
 *          The RFC 2822 date and time in GMT that the resource was created
 * @property {Date} dateUpdated -
 *          The RFC 2822 date and time in GMT that the resource was last updated
 * @property {string} url - The absolute URL of the resource
 *
 * @param {V1} version - Version of the resource
 * @param {ByocTrunkPayload} payload - The instance payload
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
ByocTrunkInstance = function ByocTrunkInstance(version, payload, sid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.sid = payload.sid; // jshint ignore:line
  this.friendlyName = payload.friendly_name; // jshint ignore:line
  this.voiceUrl = payload.voice_url; // jshint ignore:line
  this.voiceMethod = payload.voice_method; // jshint ignore:line
  this.voiceFallbackUrl = payload.voice_fallback_url; // jshint ignore:line
  this.voiceFallbackMethod = payload.voice_fallback_method; // jshint ignore:line
  this.statusCallbackUrl = payload.status_callback_url; // jshint ignore:line
  this.statusCallbackMethod = payload.status_callback_method; // jshint ignore:line
  this.cnamLookupEnabled = payload.cnam_lookup_enabled; // jshint ignore:line
  this.connectionPolicySid = payload.connection_policy_sid; // jshint ignore:line
  this.fromDomainSid = payload.from_domain_sid; // jshint ignore:line
  this.dateCreated = deserialize.iso8601DateTime(payload.date_created); // jshint ignore:line
  this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated); // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {sid: sid || this.sid, };
};

Object.defineProperty(ByocTrunkInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ByocTrunkContext(this._version, this._solution.sid);
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a ByocTrunkInstance
 *
 * @function fetch
 * @memberof Twilio.Voice.V1.ByocTrunkInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * update a ByocTrunkInstance
 *
 * @function update
 * @memberof Twilio.Voice.V1.ByocTrunkInstance#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.voiceUrl] - The URL we should call when receiving a call
 * @param {string} [opts.voiceMethod] -
 *          The HTTP method we should use with voice_url
 * @param {string} [opts.voiceFallbackUrl] -
 *          The URL we should call when an error occurs in executing TwiML
 * @param {string} [opts.voiceFallbackMethod] -
 *          The HTTP method used with voice_fallback_url
 * @param {string} [opts.statusCallbackUrl] -
 *          The URL that we should call to pass status updates
 * @param {string} [opts.statusCallbackMethod] -
 *          The HTTP method we should use to call status_callback_url
 * @param {boolean} [opts.cnamLookupEnabled] -
 *          Whether Caller ID Name (CNAM) lookup is enabled for the trunk
 * @param {string} [opts.connectionPolicySid] -
 *          Origination Connection Policy (to your Carrier)
 * @param {string} [opts.fromDomainSid] -
 *          The SID of the SIP Domain that should be used in the `From` header of originating calls
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkInstance.prototype.update = function update(opts, callback) {
  return this._proxy.update(opts, callback);
};

/* jshint ignore:start */
/**
 * remove a ByocTrunkInstance
 *
 * @function remove
 * @memberof Twilio.Voice.V1.ByocTrunkInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkInstance.prototype.remove = function remove(callback) {
  return this._proxy.remove(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ByocTrunkInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ByocTrunkInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ByocTrunkInstance.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ByocTrunkContext
 *
 * @constructor Twilio.Voice.V1.ByocTrunkContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} sid - The unique string that identifies the resource
 */
/* jshint ignore:end */
ByocTrunkContext = function ByocTrunkContext(version, sid) {
  this._version = version;

  // Path Solution
  this._solution = {sid: sid, };
  this._uri = `/ByocTrunks/${sid}`;
};

/* jshint ignore:start */
/**
 * fetch a ByocTrunkInstance
 *
 * @function fetch
 * @memberof Twilio.Voice.V1.ByocTrunkContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new ByocTrunkInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * update a ByocTrunkInstance
 *
 * @function update
 * @memberof Twilio.Voice.V1.ByocTrunkContext#
 *
 * @param {object} [opts] - Options for request
 * @param {string} [opts.friendlyName] - A string to describe the resource
 * @param {string} [opts.voiceUrl] - The URL we should call when receiving a call
 * @param {string} [opts.voiceMethod] -
 *          The HTTP method we should use with voice_url
 * @param {string} [opts.voiceFallbackUrl] -
 *          The URL we should call when an error occurs in executing TwiML
 * @param {string} [opts.voiceFallbackMethod] -
 *          The HTTP method used with voice_fallback_url
 * @param {string} [opts.statusCallbackUrl] -
 *          The URL that we should call to pass status updates
 * @param {string} [opts.statusCallbackMethod] -
 *          The HTTP method we should use to call status_callback_url
 * @param {boolean} [opts.cnamLookupEnabled] -
 *          Whether Caller ID Name (CNAM) lookup is enabled for the trunk
 * @param {string} [opts.connectionPolicySid] -
 *          Origination Connection Policy (to your Carrier)
 * @param {string} [opts.fromDomainSid] -
 *          The SID of the SIP Domain that should be used in the `From` header of originating calls
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkContext.prototype.update = function update(opts, callback) {
  if (_.isFunction(opts)) {
    callback = opts;
    opts = {};
  }
  opts = opts || {};

  var deferred = Q.defer();
  var data = values.of({
    'FriendlyName': _.get(opts, 'friendlyName'),
    'VoiceUrl': _.get(opts, 'voiceUrl'),
    'VoiceMethod': _.get(opts, 'voiceMethod'),
    'VoiceFallbackUrl': _.get(opts, 'voiceFallbackUrl'),
    'VoiceFallbackMethod': _.get(opts, 'voiceFallbackMethod'),
    'StatusCallbackUrl': _.get(opts, 'statusCallbackUrl'),
    'StatusCallbackMethod': _.get(opts, 'statusCallbackMethod'),
    'CnamLookupEnabled': serialize.bool(_.get(opts, 'cnamLookupEnabled')),
    'ConnectionPolicySid': _.get(opts, 'connectionPolicySid'),
    'FromDomainSid': _.get(opts, 'fromDomainSid')
  });

  var promise = this._version.update({uri: this._uri, method: 'POST', data: data});

  promise = promise.then(function(payload) {
    deferred.resolve(new ByocTrunkInstance(this._version, payload, this._solution.sid));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * remove a ByocTrunkInstance
 *
 * @function remove
 * @memberof Twilio.Voice.V1.ByocTrunkContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ByocTrunkInstance
 */
/* jshint ignore:end */
ByocTrunkContext.prototype.remove = function remove(callback) {
  var deferred = Q.defer();
  var promise = this._version.remove({uri: this._uri, method: 'DELETE'});

  promise = promise.then(function(payload) {
    deferred.resolve(payload);
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Voice.V1.ByocTrunkContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ByocTrunkContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ByocTrunkContext.prototype[util.inspect.custom] = function inspect(depth,
    options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ByocTrunkList: ByocTrunkList,
  ByocTrunkPage: ByocTrunkPage,
  ByocTrunkInstance: ByocTrunkInstance,
  ByocTrunkContext: ByocTrunkContext
};
