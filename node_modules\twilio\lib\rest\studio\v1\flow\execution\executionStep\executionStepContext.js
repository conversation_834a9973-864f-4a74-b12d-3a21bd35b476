'use strict';

/* jshint ignore:start */
/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
/* jshint ignore:end */

var Q = require('q');  /* jshint ignore:line */
var _ = require('lodash');  /* jshint ignore:line */
var util = require('util');  /* jshint ignore:line */
var Page = require('../../../../../../base/Page');  /* jshint ignore:line */
var values = require('../../../../../../base/values');  /* jshint ignore:line */

var ExecutionStepContextList;
var ExecutionStepContextPage;
var ExecutionStepContextInstance;
var ExecutionStepContextContext;

/* jshint ignore:start */
/**
 * Initialize the ExecutionStepContextList
 *
 * @constructor Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextList
 *
 * @param {Twilio.Studio.V1} version - Version of the resource
 * @param {string} flowSid - The SID of the Flow
 * @param {string} executionSid - The SID of the Execution
 * @param {string} stepSid - Step SID
 */
/* jshint ignore:end */
ExecutionStepContextList = function ExecutionStepContextList(version, flowSid,
    executionSid, stepSid) {
  /* jshint ignore:start */
  /**
   * @function stepContext
   * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext#
   *
   * @param {string} sid - sid of instance
   *
   * @returns {Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextContext}
   */
  /* jshint ignore:end */
  function ExecutionStepContextListInstance(sid) {
    return ExecutionStepContextListInstance.get(sid);
  }

  ExecutionStepContextListInstance._version = version;
  // Path Solution
  ExecutionStepContextListInstance._solution = {
    flowSid: flowSid,
    executionSid: executionSid,
    stepSid: stepSid
  };
  /* jshint ignore:start */
  /**
   * Constructs a execution_step_context
   *
   * @function get
   * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextList#
   *
   * @returns {Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextContext}
   */
  /* jshint ignore:end */
  ExecutionStepContextListInstance.get = function get() {
    return new ExecutionStepContextContext(
      this._version,
      this._solution.flowSid,
      this._solution.executionSid,
      this._solution.stepSid
    );
  };

  /* jshint ignore:start */
  /**
   * Provide a user-friendly representation
   *
   * @function toJSON
   * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextList#
   *
   * @returns Object
   */
  /* jshint ignore:end */
  ExecutionStepContextListInstance.toJSON = function toJSON() {
    return this._solution;
  };

  ExecutionStepContextListInstance[util.inspect.custom] = function inspect(depth,
      options) {
    return util.inspect(this.toJSON(), options);
  };

  return ExecutionStepContextListInstance;
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionStepContextPage
 *
 * @constructor Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextPage
 *
 * @param {V1} version - Version of the resource
 * @param {Response<string>} response - Response from the API
 * @param {ExecutionStepContextSolution} solution - Path solution
 *
 * @returns ExecutionStepContextPage
 */
/* jshint ignore:end */
ExecutionStepContextPage = function ExecutionStepContextPage(version, response,
    solution) {
  // Path Solution
  this._solution = solution;

  Page.prototype.constructor.call(this, version, response, this._solution);
};

_.extend(ExecutionStepContextPage.prototype, Page.prototype);
ExecutionStepContextPage.prototype.constructor = ExecutionStepContextPage;

/* jshint ignore:start */
/**
 * Build an instance of ExecutionStepContextInstance
 *
 * @function getInstance
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextPage#
 *
 * @param {ExecutionStepContextPayload} payload - Payload response from the API
 *
 * @returns ExecutionStepContextInstance
 */
/* jshint ignore:end */
ExecutionStepContextPage.prototype.getInstance = function getInstance(payload) {
  return new ExecutionStepContextInstance(
    this._version,
    payload,
    this._solution.flowSid,
    this._solution.executionSid,
    this._solution.stepSid
  );
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextPage#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionStepContextPage.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExecutionStepContextPage.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionStepContextContext
 *
 * @constructor Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextInstance
 *
 * @property {string} accountSid - The SID of the Account that created the resource
 * @property {object} context - The current state of the flow
 * @property {string} executionSid - The SID of the Execution
 * @property {string} flowSid - The SID of the Flow
 * @property {string} stepSid - Step SID
 * @property {string} url - The absolute URL of the resource
 *
 * @param {V1} version - Version of the resource
 * @param {ExecutionStepContextPayload} payload - The instance payload
 * @param {sid} flowSid - The SID of the Flow
 * @param {sid} executionSid - The SID of the Execution
 * @param {sid} stepSid - Step SID
 */
/* jshint ignore:end */
ExecutionStepContextInstance = function ExecutionStepContextInstance(version,
    payload, flowSid, executionSid, stepSid) {
  this._version = version;

  // Marshaled Properties
  this.accountSid = payload.account_sid; // jshint ignore:line
  this.context = payload.context; // jshint ignore:line
  this.executionSid = payload.execution_sid; // jshint ignore:line
  this.flowSid = payload.flow_sid; // jshint ignore:line
  this.stepSid = payload.step_sid; // jshint ignore:line
  this.url = payload.url; // jshint ignore:line

  // Context
  this._context = undefined;
  this._solution = {flowSid: flowSid, executionSid: executionSid, stepSid: stepSid, };
};

Object.defineProperty(ExecutionStepContextInstance.prototype,
  '_proxy', {
    get: function() {
      if (!this._context) {
        this._context = new ExecutionStepContextContext(
          this._version,
          this._solution.flowSid,
          this._solution.executionSid,
          this._solution.stepSid
        );
      }

      return this._context;
    }
});

/* jshint ignore:start */
/**
 * fetch a ExecutionStepContextInstance
 *
 * @function fetch
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextInstance#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionStepContextInstance
 */
/* jshint ignore:end */
ExecutionStepContextInstance.prototype.fetch = function fetch(callback) {
  return this._proxy.fetch(callback);
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextInstance#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionStepContextInstance.prototype.toJSON = function toJSON() {
  let clone = {};
  _.forOwn(this, function(value, key) {
    if (!_.startsWith(key, '_') && ! _.isFunction(value)) {
      clone[key] = value;
    }
  });
  return clone;
};

ExecutionStepContextInstance.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};


/* jshint ignore:start */
/**
 * Initialize the ExecutionStepContextContext
 *
 * @constructor Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextContext
 *
 * @param {V1} version - Version of the resource
 * @param {sid} flowSid - The SID of the Flow
 * @param {sid} executionSid - The SID of the Execution
 * @param {sid} stepSid - Step SID
 */
/* jshint ignore:end */
ExecutionStepContextContext = function ExecutionStepContextContext(version,
    flowSid, executionSid, stepSid) {
  this._version = version;

  // Path Solution
  this._solution = {flowSid: flowSid, executionSid: executionSid, stepSid: stepSid, };
  this._uri = `/Flows/${flowSid}/Executions/${executionSid}/Steps/${stepSid}/Context`;
};

/* jshint ignore:start */
/**
 * fetch a ExecutionStepContextInstance
 *
 * @function fetch
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextContext#
 *
 * @param {function} [callback] - Callback to handle processed record
 *
 * @returns {Promise} Resolves to processed ExecutionStepContextInstance
 */
/* jshint ignore:end */
ExecutionStepContextContext.prototype.fetch = function fetch(callback) {
  var deferred = Q.defer();
  var promise = this._version.fetch({uri: this._uri, method: 'GET'});

  promise = promise.then(function(payload) {
    deferred.resolve(new ExecutionStepContextInstance(
      this._version,
      payload,
      this._solution.flowSid,
      this._solution.executionSid,
      this._solution.stepSid
    ));
  }.bind(this));

  promise.catch(function(error) {
    deferred.reject(error);
  });

  if (_.isFunction(callback)) {
    deferred.promise.nodeify(callback);
  }

  return deferred.promise;
};

/* jshint ignore:start */
/**
 * Provide a user-friendly representation
 *
 * @function toJSON
 * @memberof Twilio.Studio.V1.FlowContext.ExecutionContext.ExecutionStepContext.ExecutionStepContextContext#
 *
 * @returns Object
 */
/* jshint ignore:end */
ExecutionStepContextContext.prototype.toJSON = function toJSON() {
  return this._solution;
};

ExecutionStepContextContext.prototype[util.inspect.custom] = function
    inspect(depth, options) {
  return util.inspect(this.toJSON(), options);
};

module.exports = {
  ExecutionStepContextList: ExecutionStepContextList,
  ExecutionStepContextPage: ExecutionStepContextPage,
  ExecutionStepContextInstance: ExecutionStepContextInstance,
  ExecutionStepContextContext: ExecutionStepContextContext
};
